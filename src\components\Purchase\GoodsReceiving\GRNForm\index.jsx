import React, { Component, Fragment } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Alert, DatePicker, Select, Upload, notification, Button, Drawer, Switch, Radio, Checkbox, Tooltip,
  Card,
} from 'antd';
import dayjs from 'dayjs';
import {
  PlusOutlined, PlusCircleFilled, EditFilled, UploadOutlined,
} from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark, faPlusCircle, faCircleInfo, } from '@fortawesome/free-solid-svg-icons';
import Constants, {
  INFINITE_EXPIRY_DATE, QUANTITY, DEFAULT_CUR_ROUND_OFF,
} from '@Apis/constants';
import closeIcon from '@Images/icons/icon-close-blue.png';

// Components
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import H3FormInput from '@Uilib/h3FormInput';
import H3Modal from '@Uilib/H3Modal';
import BatchesList from '@Components/Common/BatchesList';
import SelectSellerV2 from '../../../Common/SelectSellerV2';
import ViewPurchaseOrder from '../../../Common/ViewPurchaseOrder';
import SelectTaxType from '../../../Admin/Common/SelectTaxType';
import SelectDepartment from '@Components/Common/SelectDepartment';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import TagSelector from '@Components/Common/Selector/TagSelector';
import SelectExtraCharge from '../../../Admin/Common/SelectExtraCharge';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import FormLoadingSkull from '@Components/Common/formLoadingSkull';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import ErrorHandle from '../../../Common/ErrorHandle';
import VendorRating from '../../../Common/VendorRating';
import RestrictedAccessMessage from '../../../Common/RestrictedAccess/RestrictedAccessMessage';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import PRZButton from '../../../Common/UI/PRZButton';
import PRZConfirmationPopover from '../../../Common/UI/PRZConfirmationPopover';
import PRZInput from '../../../Common/UI/PRZInput';
import PRZText from '../../../Common/UI/PRZText';
import ChargesTaxInput from '../../../Common/ChargesTaxInput';
import PRZSelect from '../../../Common/UI/PRZSelect/index.jsx';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput/index.jsx';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import SelectPaymentRemark from '@Components/Common/SelectPaymentRemark';

// Helpers
import Helpers from '@Apis/helpers';
import SelectOrdersForGRN from './helpers.jsx';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';

// Actions
import { GetPurchaseOrders } from '@Modules/purchase/purchaseOrder';
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import SellerActions from '@Actions/sellerActions';
import GRNActions from '@Actions/grnActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import OCRActions from '@Actions/ocrActions';
import TenantActions from '@Actions/tenantActions';
import TagActions from '@Actions/tagActions';
import ExtraChargesActions from '@Actions/configurations/extraChargesAction';
import VendorRatingActions from '../../../../actions/configurations/vendorRatingActions';
import InventoryLocationActions from '../../../../actions/settings/inventoryLocationActions';
import TaxActions from '@Actions/taxActions';

// Current File Imports
import GRNFormLines from './GRNFormLines';
import grnErrorList from './GrnErrors';
import './style.scss';

const { Option } = Select;

const uploadButtonFormLevel = (
  <Button icon={<UploadOutlined />}>Click to Upload</Button>
);
const uploadButtonForUploadingAttachments = (
  <div>
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  </div>
);
class GRNForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      grnDate: dayjs(),
      invoiceDate: dayjs(),
      dueDate: dayjs(),
      toggleRejectedBatches: false,
      grnTypeValue: 'Purchase Order',
      grnTableData: [],
      chargeData: [],
      isLineWiseDiscount: false,
      showModal: false,
      getHistory: '',
      latestGrnId: '',
      taxTypeName: 'TDS',
      selectedCurrencyName: '',
      selectedCurrencyID: '',
      allStock: true,
      tenantDepartmentId: '',
      isMultipleBatchModeEnabled: false,
      multipleBatchValidation: false,
      charge1Name: 'Freight',
      charge1Value: '',
      isBatchValid: false,
      discountType: 'Percent',
      freightTaxId: 'Not Applicable',
      freightTax: null,
      openFreightTax: false,
      visibleLineCfs: [],
      isAutomaticConversionRate: null,
      currencyConversionRate: '',
      selectedTenant: null,
      updateDocumentReason: '',
      batchNumbers: new Map([]),
      selectedTenantTallyIntegrationId: props?.user?.tenant_info?.it_id,
      narration: '',
      freightTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
      paymentTerms: 0,
      paymentRemarks: '',
    };
  }

  componentDidMount() {
    const {
      user, match, getDocCFV2, getGRNById, getInventoryLocations, selectedPoForGrn, inventoryLocations, getVendorRating, getCurrencies, getGRNByIdSuccess, location, getCharges, getPurchaseOrders, getPurchaseOrdersV2, getTaxes,
    } = this.props;
    getGRNByIdSuccess(null);
    getVendorRating(user?.tenant_info?.org_id);
    if (match?.params?.grnId) {
      getGRNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), match?.params?.grnId, (grn) => {
        const linkedPoIds = [...new Set(grn?.grn_lines?.map(item => item?.po_id))]?.filter(Boolean)?.join(',');
        if (grn?.tenant_info?.tenant_id && grn?.tenant_seller_info?.tenant_seller_id) {
          getPurchaseOrdersV2({
            query: {
              org_id: user?.org_id,
              tenant_id: grn?.tenant_info?.tenant_id,
              tenant_seller_id: grn?.tenant_seller_info?.tenant_seller_id,
              status: 'ISSUED',
              page: 1,
              limit: 30,
              exclude_job_works_po: true,
              exclude_subcontractor_po: true,
              linked_po_ids: linkedPoIds,
            },
          });
        }
        getInventoryLocations(grn?.tenant_department_id, '', null, true, null, null, (location) => {
          this.setState({
            inventoryLocationId: location?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
            inventoryLocationPath: location?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
            grnNumber: grn?.grn_number || '',
          });
        });
      });
    }
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'GOOD_RECEIVING_NOTES,BATCH',
    };
    getDocCFV2(payload);
    if (selectedPoForGrn
      && (!inventoryLocations || (inventoryLocations && inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.tenant_department_id !== selectedPoForGrn?.tenant_department_id))) {
      getInventoryLocations(selectedPoForGrn?.tenant_department_id, '', null, true, null, null, (location) => {
        this.setState({
          inventoryLocationId: location?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
          inventoryLocationPath: location?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
          tenantDepartmentId: selectedPoForGrn?.tenant_department_id,
          selectedTenant: selectedPoForGrn?.tenant_id,
          selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedPoForGrn?.tenant_id)?.it_id,
        });
      });
    }
    getCurrencies();
    getCharges(user?.tenant_info?.org_id, 'PURCHASE', (charges) => {
      const freight = charges?.find((i) => (i?.ledger_name === 'Freight Charge' && i?.is_system_charge));
      if (freight) {
        this.setState({ freightSacCode: freight?.charge_sac_code });
      }
    });
    getTaxes(user?.tenant_info?.org_id, 1, 1000, null, true);
  }

  componentWillUnmount() {
    const { getGRNByIdSuccess } = this.props;
    getGRNByIdSuccess(null);
  }

  getGrnErrors() {
    const { match, selectedPoForGrn, user } = this.props;
    const { selectedSeller, invoiceNumber, cfGoodReceivingNotesDoc, grnTypeValue, grnDate, selectedPoValue, shippingAddress, cfGoodReceivingNotesLine, grnTableData, isMultipleBatchModeEnabled, isBatchValid, ewayBillList, ewayBillNumber, vendorAddress, checkedRecipients, toRecipients, invoiceDate, selectedTenant, grnNumber } = this.state;
    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
    const grnId = match?.params?.grnId;
    const { docLevelError, lineLevelError } = grnErrorList({ selectedSeller, invoiceNumber, cfGoodReceivingNotesDoc, grnTypeValue, grnDate, selectedPoValue, shippingAddress, cfGoodReceivingNotesLine, grnTableData, isMultipleBatchModeEnabled, isBatchValid, grnId, ewayBillNumber, ewayBillList, vendorAddress, checkedRecipients, toRecipients, selectedPoForGrn, user, invoiceDate, selectedTenant, grnNumber, isApInvoiceEnabled });
    return { docLevelError: docLevelError ?? [], lineLevelError: lineLevelError ?? [] };
  }

  static getDerivedStateFromProps(props, state) {
    const {
      CurrenciesResults, selectedPoForGrn, selectedGRN, user, location, tenantDepartmentId, getInventoryLocations, inventoryLocations, selectedPo, selectedTenant, taxesGroup,
    } = props;
    const { selectedCurrencyID, grnTypeValue } = state;

    const dataForOcrGrn = location?.state?.dataForGrn;

    // create grn from ocr data prefilling
    if (dataForOcrGrn && !props?.selectedPoForGrn?.po_id) {
      if (props.user && dataForOcrGrn && !state.grnTableData?.length && !state.isDataFromOcrFilled && props.cfV2DocGoodReceivingNotes?.data?.success) {
        const batchCustomFields = CustomFieldHelpers.getCfStructure(props.cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);
        return {
          ...state,
          grnTableData: dataForOcrGrn?.grn_lines?.map((item) => {
            const newBatch = {
              tenant_product_id: item?.tenant_product_id,
              expiry_date: item?.product_info?.expiry_days > 0 ? (dayjs().endOf('day').add(item?.product_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
              lot_number: '',
              custom_batch_number: `${item?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_info?.product_batch_counter}`,
              cost_price: (item?.offer_price || 0),
              mrp: item?.selling_price || 0,
              manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
              expiryDateFormat: item?.product_sku_info?.expiry_date_format,
              selling_price: 0,
              tenant_department_id: dataForOcrGrn?.tenantDepartmentId,
              uom_id: item?.uom_id,
              manual_entry: false,
              inventory_location_id: item?.selectedLocation?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
              inventory_location_path: item?.selectedLocation?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
              is_rejected_batch: false,
              production_route_line_id: item?.production_route_line_id,
              custom_fields: batchCustomFields,
            };
            const parentKey = uuidv4();
            const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF)) || 0;
            const quantity = item?.quantity ?? 0;
            const offerPrice = item?.offer_price ?? 0;
            const taxableValue = item?.is_discount_in_percent === 'Percent'
              ? (quantity * offerPrice) * (1 - discountValue / 100)
              : Math.max(quantity * offerPrice - discountValue, 0);
            const lineCFs = props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.getCfStructure(props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [], false) : [];
            return ({
              ...item,
              product_sku_info: item?.product_info || item,
              manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
              expiryDateFormat: item?.product_sku_info?.expiry_date_format,
              key: parentKey,
              product_sku_name: item?.product_info?.product_sku_name,
              lineDiscountType: 'Percent',
              quantity: item?.quantity || 0,
              secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
              secondaryUomId: item?.product_sku_info?.secondary_uom_id,
              secondary_uom_qty: 0,
              offer_price: item?.offer_price,
              taxId: item?.tax_info?.tax_id,
              taxInfo: item?.tax_info,
              uomId: item?.uom_info?.uom_id || item?.uom_id,
              productCategoryInfo: item?.product_category_info || {},
              uomInfo: item?.uom_info,
              uom_info: item?.uom_info,
              received_qty: item?.quantity || 0,
              remarks: item?.product_info?.description?.replace(/<[^>]+>/g, ''),
              remarkRequired: !!item?.product_info?.description?.replace(/<[^>]+>/g, ''),
              product_batches: [],
              child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_info, item?.selectedPo?.billing_address?.state, item?.selectedPo?.seller_address?.state)?.tax_info?.child_taxes,
              selectedBatch: newBatch,
              lineCustomFields: lineCFs,
              available_batches: item?.product_batches
                ? [
                  {
                    ...newBatch,
                  },
                  ...item?.product_batches.map((batch) => ({
                    ...batch,
                    custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
                  })),
                ]
                : [
                  {
                    ...newBatch,
                  },
                ],
              multiple_batch_info: [{
                key: uuidv4(),
                ...newBatch,
                parentKey,
                sq_no: 1,
                quantity: '',
                ar_number: '',
                expiryDays: item?.product_sku_info?.expiry_days,
              }],
            });
          }),
          selectedPo: dataForOcrGrn?.selectedPo,
          tenantDepartmentId: dataForOcrGrn?.tenantDepartmentId || user?.tenant_info?.default_store_id,
          selectedTenantSeller: dataForOcrGrn?.selectedSeller?.tenant_seller_id,
          gstNumber: dataForOcrGrn?.selectedPo?.tenant_seller_info?.gst_number,
          selectedSeller: dataForOcrGrn?.selectedSeller,
          shippingAddress: dataForOcrGrn?.selectedPo?.shipping_address,
          vendorAddress: dataForOcrGrn?.grnTypeValue === 'ADHOC' ? dataForOcrGrn?.vendorAddress : dataForOcrGrn?.selectedPo?.seller_address,
          selectedAddressType: 'SELLER',
          invoiceDate: dataForOcrGrn?.invoice_date ? dayjs(dataForOcrGrn?.invoice_date, 'DD-MM-YYYY') : null,
          invoiceNumber: dataForOcrGrn?.invoice_number,
          grnTypeValue: dataForOcrGrn?.grnTypeValue,
          selectedPoValue: dataForOcrGrn?.selectedPoValue,
          isDataFromOcrFilled: true,
          fileList: dataForOcrGrn?.ocrDocData,
          selectedCurrencyName: dataForOcrGrn?.selectedPo ? dataForOcrGrn?.selectedPo?.org_currency_info : '',
          selectedCurrencyID: dataForOcrGrn?.selectedPo ? dataForOcrGrn?.selectedPo?.org_currency_info?.org_currency_id : '',
          isAutomaticConversionRate: dataForOcrGrn?.selectedPo ? false : null,
          currencyConversionRate: dataForOcrGrn?.selectedPo ? dataForOcrGrn?.selectedPo?.conversion_rate : '',
        };
      }
    }

    //  adhoc grn
    if (props?.match?.params?.grnId && !props?.selectedPoForGrn?.po_id) {
      if (props.user && props?.selectedGRN && !state.grnTableData?.length && props?.cfV2DocGoodReceivingNotes?.data?.success) {
        props.getSellers('', selectedTenant, 1, 30, props?.selectedGRN?.tenant_seller_id);
        if (props?.selectedGRN) {
          props.getPurchaseOrderById(props?.selectedGRN?.grn_entity_id, Helpers.getTenantEntityPermission(props.user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','), () => { });
        }
        const getAvailableBatches = (item) => (item?.product_batches?.[0]?.batch_id ? item?.available_batches || [] : item?.available_batches ? [...item?.product_batches, ...item?.available_batches] : [...item?.product_batches]);

        function checkGRNType() {
          if (props?.selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE') {
            return 'ADHOC';
          }
          if (props?.selectedGRN?.grn_entity_type === 'PURCHASE_ORDER' && !props?.selectedGRN?.grn_entity_id) {
            return 'MULTIPO';
          }
          return 'Purchase Order';
        }

        return {
          ...state,
          selectedPoValue: props?.selectedGRN?.linked_pos?.map((item) => ({
            key: item?.po_id,
            value: item?.po_id,
            label: [`#${item?.po_number}`, ''],
          })),
          grnTableData: props?.selectedGRN?.grn_lines?.map((item) => {
            const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
            const taxableValue = item?.is_discount_in_percent
              ? (item.received_qty * item.offer_price) * (1 - discountValue / 100)
              : Math.max((item.received_qty * item.offer_price) - discountValue, 0);
            let productBatchPoToGrn = getAvailableBatches(item)?.filter((batch) => !batch.is_rejected_batch)?.[0];
            productBatchPoToGrn = {
              ...productBatchPoToGrn,
              custom_fields: CustomFieldHelpers.getCfStructure(productBatchPoToGrn?.custom_fields, true),
            };
            let productBatchesAdhocGrn = item?.product_batches?.[0];
            productBatchesAdhocGrn = {
              ...productBatchesAdhocGrn,
              custom_fields: CustomFieldHelpers.getCfStructure(productBatchesAdhocGrn?.custom_fields, true),
            };
            return {
              ...item,
              key: uuidv4(),
              taxId: item?.tax_group_info?.tax_id,
              taxInfo: item?.tax_group_info,
              child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_group_info, selectedGRN?.tenant_billing_address?.state, selectedGRN?.seller_address_info?.state)?.tax_info?.child_taxes,
              uomId: item?.uom_info[0]?.uom_id,
              uomInfo: item?.uom_info[0],
              uom_info: item?.uom_info[0],
              uomGroup: item?.product_sku_info?.group_id || item?.product_sku_info?.purchase_uom_info?.group_id,
              uom_list: item?.product_sku_info?.uom_list,
              expiryDate: item.expiry_date,
              otherGrnQty: (item?.ordered_qty - item?.total_received_qty),
              available_batches: getAvailableBatches(item),
              secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
              secondaryUomId: item?.product_sku_info?.secondary_uom_id,
              secondary_uom_qty: item?.secondary_uom_qty,
              manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
              expiryDateFormat: item?.product_sku_info?.expiry_date_format,
              selectedBatch: props?.selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE' ? productBatchesAdhocGrn : productBatchPoToGrn,
              expiryDays: item?.product_sku_info?.expiry_days,
              remarks: item?.remark,
              remarkRequired: !!item?.remark?.replace(/<[^>]+>/g, ''),
              discount: discountValue,
              quantity: item?.received_qty,
              invoice_quantity: item?.invoice_quantity,
              po_line_id: item?.grn_entity_line_id,
              tally_purchase_account: item?.tally_purchase_account,
              nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
              product_sku_name: item?.product_sku_info?.product_sku_name,
              lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
              productCategoryInfo: item?.product_sku_info?.product_category_info || {},
              multiple_batch_info: item?.product_batches?.map((batch) => ({
                key: uuidv4(),
                ...batch,
                grn_line_id: item?.grn_line_id,
                custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
              })),
              lineCustomFields: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields.filter((item) => item?.is_active), item?.grn_line_custom_fields || []), // check
            };
          }),
          selectedPo: props?.selectedGRN?.grn_entity_id,
          checkedRecipients: props?.selectedGRN?.is_automatic_notification_enabled,
          toRecipients: props?.selectedGRN?.notification_recipients || [],
          tenantDepartmentId: props?.selectedGRN?.tenant_department_id,
          grnTypeValue: checkGRNType(),
          terms: props?.selectedGRN?.remark,
          selectedTenantSeller: props?.selectedGRN?.tenant_seller_info?.tenant_seller_id,
          gstNumber: props?.selectedGRN?.tenant_seller_info?.gst_number,
          selectedSellerId: props?.selectedGRN?.seller_id,
          grnDate: dayjs(props?.selectedGRN?.grn_date_time),
          ewayBillNumber: props?.selectedGRN?.e_way_bill_number,
          ewayBillList: props?.selectedGRN?.e_way_bill,
          invoiceDate: props?.selectedGRN?.invoice_date ? dayjs(props?.selectedGRN?.invoice_date) : null,
          invoiceNumber: props?.selectedGRN?.invoice_number,
          fileList: props?.selectedGRN?.attachments,
          purchaseAccount: props?.selectedGRN?.tally_purchase_account,
          dueDate: props?.selectedGRN?.grn_due_date ? dayjs(props?.selectedGRN?.grn_due_date) : null,
          chargeData:
            props?.selectedGRN?.other_charges?.map((otherCharge) => ({
              ...otherCharge,
              chargeKey: uuidv4(),
              chargesTaxId: otherCharge?.tax_info?.tax_id,
              chargesSacCode: otherCharge?.charge_sac_code,
              chargesTaxInfo: otherCharge?.tax_info,
              chargesTax: otherCharge?.tax_info?.tax_value,
              tallyLedgerName: otherCharge?.ledger_name || null,
              chargesTaxData: {
                ...otherCharge?.tax_info,
                child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, otherCharge?.tax_info, props?.selectedGRN?.tenant_billing_address?.state, props?.selectedGRN?.seller_address_info?.state)?.tax_info?.child_taxes,
              },
            })) || [],
          discountPercentage: props?.selectedGRN?.is_discount_in_percent ? props?.selectedGRN?.discount_percentage : props?.selectedGRN?.discount_amount,
          discountType: props?.selectedGRN?.is_discount_in_percent ? 'Percent' : 'Amount',
          isLineWiseDiscount: !props?.selectedGRN?.is_line_wise_discount,
          taxType: props?.selectedGRN?.tcs_id ? props?.selectedGRN?.tcs_id : props?.selectedGRN?.tds_id,
          taxTypeId: props?.selectedGRN?.tcs_info ? props?.selectedGRN?.tcs_info?.tax_id : props?.selectedGRN?.tds_info?.tax_id,
          taxTypeInfo: props?.selectedGRN?.tcs_info ? props?.selectedGRN?.tcs_info : props?.selectedGRN?.tds_info,
          selectedCurrencyName: props?.selectedGRN?.org_currency_info,
          selectedCurrencyID: props?.selectedGRN?.org_currency_info?.org_currency_id,
          taxTypeName: props?.selectedGRN?.tcs_info ? props?.selectedGRN?.tcs_info?.tax_type_name : props?.selectedGRN?.tds_info?.tax_type_name,
          charge1Name: props?.selectedGRN?.charge_1_name || 'Freight',
          charge1Value: props?.selectedGRN?.charge_1_value,
          freightTaxId: props?.selectedGRN?.freight_tax_id || 'Not Applicable',
          freightTax: props?.selectedGRN?.freight_tax_info?.tax_value,
          freightTaxInfo: props?.selectedGRN?.freight_tax_info,
          freightTaxData: {
            ...props?.selectedGRN?.freight_tax_info,
            child_taxes: Helpers.computeTaxation(props?.selectedGRN?.charge_1_value, props?.selectedGRN?.freight_tax_info, props?.selectedGRN?.tenant_billing_address?.state, props?.selectedGRN?.seller_address_info?.state)?.tax_info?.child_taxes
          },
          freightSacCode: props?.selectedGRN?.freight_sac_code,
          createExpenseCheckbox: props?.selectedGRN?.transporter_id || props?.selectedGRN?.vehicle_number,
          transporterId: props?.selectedGRN?.transporter_id,
          vehicleNumber: props?.selectedGRN?.vehicle_number,
          transporterBillNumber: props?.selectedGRN?.transporter_bill_number,
          billingAddress: user?.tenant_info?.state,
          vendorAddress: props?.selectedGRN?.seller_address_info,
          selectedTags: props?.selectedGRN?.tags,
          isAutomaticConversionRate: false,
          currencyConversionRate: props?.selectedGRN?.conversion_rate,
          selectedTenant: props?.selectedGRN?.tenant_info?.tenant_id,
          selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === props?.selectedGRN?.tenant_info?.tenant_id)?.it_id,
          narration: props?.selectedGRN?.narration || '',
          isBatchValid: true,
          paymentTerms: props?.selectedGRN?.payment_terms?.[0]?.due_days || 0,
          paymentRemarks: props?.selectedGRN?.payment_terms?.[0]?.remark || '',
        };
      }
      if (props.selectedGRN?.grn_id && props?.cfV2DocGoodReceivingNotes?.data?.success && !state.isUserReadyForUpdate) {
        // Already Used Custom  Fields
        const oldCustomField = props.selectedGRN?.custom_fields || [];
        const oldLineCustomField = props.selectedGRN.grn_lines?.[0]?.grn_line_custom_fields || [];

        return {
          ...state,
          cfGoodReceivingNotesDoc: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes?.data?.document_custom_fields, oldCustomField),
          cfGoodReceivingNotesLine: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes.data.document_line_custom_fields, oldLineCustomField) || [],
          visibleLineCfs: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes.data.document_line_custom_fields, oldLineCustomField).filter((item) => item.isActive) || [],
          isUserReadyForUpdate: true,
        };
      }
    }

    // create grn from po
    if (props?.selectedPoForGrn?.po_id && !state?.grnTableData?.length && props.selectedPoForGrn && props?.inventoryLocations
      && props?.inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.tenant_department_id === props?.selectedPoForGrn?.tenant_department_id && props?.cfV2DocGoodReceivingNotes?.data?.success
      && !state.selectedPoForGrn && !state.isUserReady
      && taxesGroup?.success) {
      const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
      const batchCustomFields = CustomFieldHelpers.getCfStructure(props?.cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);
      const isVendorOverseas = (selectedPoForGrn?.seller_info?.seller_type === 'OVERSEAS' || selectedPoForGrn?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
      const grnData = props?.selectedPoForGrn?.purchase_order_lines?.map((item) => {
        const automaticArNumber = user?.tenant_info?.inventory_config?.settings?.enable_auto_generation_of_ar_number;
        const newBatch = {
          tenant_product_id: item?.tenant_product_id,
          expiry_date: item?.product_sku_info?.expiry_days > 0 ? (dayjs().endOf('day').add(item?.product_sku_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
          lot_number: '',
          custom_batch_number: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
          cost_price: isApInvoiceEnabled ? 0 : (item?.offer_price && props?.selectedPoForGrn?.conversion_rate) ? (item?.offer_price * props?.selectedPoForGrn?.conversion_rate)?.toFixed(DEFAULT_CUR_ROUND_OFF) : (item?.offer_price || 0),
          mrp: item?.product_sku_info?.selling_price || 0,
          manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: item?.product_sku_info?.expiry_date_format,
          selling_price: 0,
          tenant_department_id: props.selectedPoForGrn?.tenant_department_id,
          uom_id: item?.uom_id,
          manual_entry: false,
          inventory_location_id: props?.inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
          inventory_location_path: props?.inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
          is_rejected_batch: false,
          production_route_line_id: item?.production_route_line_id,
          custom_fields: batchCustomFields,
        };
        const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const parentKey = uuidv4();
        return ({
          ...item,
          key: parentKey,
          received_qty: '',
          total_pending_qty: 0,
          taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
          taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_group_info,
          child_taxes: Helpers.computeTaxation((item.received_qty * item.offer_price), isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_group_info, selectedPoForGrn?.billing_address?.state, selectedPoForGrn?.seller_address?.state)?.tax_info?.child_taxes,
          uomId: item?.uom_id,
          uomInfo: item?.uom_info,
          discount: discountValue,
          discountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
          selectedBatch: newBatch,
          secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
          secondaryUomId: item?.product_sku_info?.secondary_uom_id,
          secondary_uom_qty: 0,
          manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: item?.product_sku_info?.expiry_date_format,
          available_batches: item?.available_batches
            ? [
              {
                ...newBatch,
              },
              ...item?.available_batches?.map((batch) => ({
                ...batch,
                custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
              })),
            ]
            : [
              {
                ...newBatch,
              },
            ],
          expiryDays: item?.product_sku_info?.expiry_days,
          productCategoryInfo: item?.product_sku_info?.product_category_info || {},
          tally_purchase_account: item?.product_sku_info?.tally_purchase_account,
          nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
          remarks: item?.remark?.replace(/<[^>]+>/g, ''),
          remarkRequired: !!item?.remark?.replace(/<[^>]+>/g, ''),
          lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
          multiple_batch_info: [{
            key: uuidv4(),
            ...newBatch,
            parentKey,
            sq_no: 1,
            quantity: '',
            ar_number: '',
            expiryDays: item?.product_sku_info?.expiry_days,
          }],
          lineCustomFields: FormHelpers.lineSystemFieldValue(CustomFieldHelpers.getCfStructure(props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.filter((item) => item?.is_active)), item?.po_line_custom_fields),
          lineStatusPendingQty: 0,
          po_line_status: item?.po_line_status?.map((item) => ({ ...item, line_status_received_qty: 0 })),
          offer_price: isApInvoiceEnabled ? 0 : item?.offer_price,
        });
      });

      const grnChargeData =
        props?.selectedPoForGrn?.other_charges?.map((otherCharge) => ({
          ...otherCharge,
          chargeKey: uuidv4(),
          chargesTaxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : otherCharge?.tax_info?.tax_id,
          chargesSacCode: otherCharge?.charge_sac_code,
          chargesTaxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info,
          chargesTax: isVendorOverseas ? 0 : otherCharge?.tax_info?.tax_value,
          tallyLedgerName: otherCharge?.ledger_name || null,
          chargesTaxData: {
            ...otherCharge?.tax_info,
            child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info, props?.selectedPoForGrn?.billing_address?.state, props?.selectedPoForGrn?.seller_address?.state)?.tax_info?.child_taxes,
          },
        })) || [];

      const dueDate = dayjs().add(Number(props?.selectedPoForGrn?.payment_terms?.[0]?.due_days || 0), 'days');
      const oldLineCustomField = props?.selectedPoForGrn?.purchase_order_lines?.[0]?.po_line_custom_fields || [];
      const oldCustomField = props?.selectedPoForGrn?.custom_fields || [];
      return {
        ...state,
        selectedTenant: props?.selectedPoForGrn?.tenant_id,
        fileList: props?.selectedPoForGrn?.attachments,
        grnTableData: FormHelpers.updatedBatchNumber(grnData),
        chargeData: grnChargeData,
        selectedPoForGrn: props.selectedPoForGrn,
        tenantDepartmentId: props.selectedPoForGrn?.tenant_department_id,
        dueDate,
        isUserReady: true,
        discountPercentage: props.selectedPoForGrn.is_discount_in_percentage ? props.selectedPoForGrn?.discount_percentage.toFixed(DEFAULT_CUR_ROUND_OFF) : props.selectedPoForGrn?.discount_amount.toFixed(DEFAULT_CUR_ROUND_OFF),
        discountType: props.selectedPoForGrn.is_discount_in_percent ? 'Percent' : 'Amount',
        isLineWiseDiscount: !props.selectedPoForGrn?.is_line_wise_discount,
        taxType: isVendorOverseas ? null : props?.selectedPoForGrn?.tcs_id ? props?.selectedPoForGrn?.tcs_id : props?.selectedPoForGrn?.tds_id,
        taxTypeId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : (props?.selectedPoForGrn?.tcs_info ? props?.selectedPoForGrn?.tcs_info?.tax_id : props?.selectedPoForGrn?.tds_info?.tax_id),
        taxTypeInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (props?.selectedPoForGrn?.tcs_info ? props?.selectedPoForGrn?.tcs_info : props?.selectedPoForGrn?.tds_info),
        selectedCurrencyID: props?.selectedPoForGrn?.org_currency_info?.org_currency_id,
        selectedCurrencyName: props?.selectedPoForGrn?.org_currency_info,
        taxTypeName: isVendorOverseas ? '' : (props?.selectedPoForGrn?.tcs_info ? props?.selectedPoForGrn?.tcs_info?.tax_type_name : props?.selectedPoForGrn?.tds_info?.tax_type_name),
        charge1Name: props?.selectedPoForGrn?.charge_1_name || 'Freight',
        gstNumber: props?.selectedPoForGrn?.tenant_seller_info?.gst_number,
        charge1Value: props?.selectedPoForGrn?.charge_1_value,
        selectedTags: props?.selectedPoForGrn?.tags,
        freightTaxId: props?.selectedPoForGrn?.freight_tax_id || 'Not Applicable',
        freightTax: props?.selectedPoForGrn?.freight_tax_info?.tax_value,
        freightTaxInfo: props?.selectedPoForGrn?.freight_tax_info,
        freightTaxData: {
          ...props?.selectedPoForGrn?.freight_tax_info,
          child_taxes: Helpers.computeTaxation(props?.selectedPoForGrn?.charge_1_value, isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : props?.selectedPoForGrn?.freight_tax_info, props?.selectedPoForGrn?.billing_address?.state, props?.selectedPoForGrn?.seller_address?.state)?.tax_info?.child_taxes
        },
        freightSacCode: props?.selectedPoForGrn?.freight_sac_code,
        createExpenseCheckbox: selectedPoForGrn?.transporter_id || selectedPoForGrn?.vehicle_number,
        transporterId: selectedPoForGrn?.transporter_id,
        vehicleNumber: selectedPoForGrn?.vehicle_number,
        transporterBillNumber: selectedPoForGrn?.transporter_bill_number,
        cfGoodReceivingNotesLine: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields, oldLineCustomField) ?? [],
        cfGoodReceivingNotesDoc: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes?.data?.document_custom_fields, oldCustomField) ?? [],
        visibleLineCfs: CustomFieldHelpers.mergeCustomFields(props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields, oldLineCustomField).filter((item) => item.isActive) ?? [],
        isAutomaticConversionRate: false,
        currencyConversionRate: props?.selectedPoForGrn?.conversion_rate,
        vendorAddress: props?.selectedPoForGrn?.seller_address,
        selectedSeller: props?.selectedPoForGrn?.seller_info,
        paymentTerms: props?.selectedPoForGrn?.payment_terms?.[0]?.due_days || 0,
        paymentRemarks: props?.selectedPoForGrn?.payment_terms?.[0]?.remark || '',
      };
    }

    if (props.cfV2DocGoodReceivingNotes && !state.isUserReadyOne && !props?.selectedPoForGrn?.po_id) {
      const lineCFs = props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.getCfStructure(props.cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [], false) : [];

      return {
        ...state,
        cfGoodReceivingNotesDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocGoodReceivingNotes?.data?.document_custom_fields, true) || [],
        cfGoodReceivingNotesLine: lineCFs,
        visibleLineCfs: lineCFs,
        isUserReadyOne: true,
        selectedTenant: props?.user?.tenant_info?.tenant_id,
      };
    }

    if (CurrenciesResults && CurrenciesResults?.length > 0 && (grnTypeValue === 'ADHOC' || dataForOcrGrn || selectedPoForGrn || selectedGRN || selectedPo)) {
      const defaultCurrency = CurrenciesResults?.find((currency) => currency.is_default === true);

      if (defaultCurrency && !state.selectedCurrencyID && selectedPoForGrn) {
        return {
          ...state,
          selectedCurrencyID: selectedPoForGrn?.org_currency_info?.org_currency_id,
          selectedCurrencyName: selectedPoForGrn?.org_currency_info,
          isAutomaticConversionRate: false,
          currencyConversionRate: selectedPoForGrn?.conversion_rate,
        };
      }

      if (defaultCurrency && !state.selectedCurrencyID && selectedGRN) {
        return {
          ...state,
          selectedCurrencyID: selectedGRN?.org_currency_info?.org_currency_id,
          selectedCurrencyName: selectedGRN?.org_currency_info,
          isAutomaticConversionRate: false,
          currencyConversionRate: selectedGRN?.conversion_rate,
        };
      }

      if (defaultCurrency && !state.selectedCurrencyID && selectedPo) {
        return {
          ...state,
          selectedCurrencyID: selectedPo?.org_currency_info?.org_currency_id,
          selectedCurrencyName: selectedPo?.org_currency_info,
          isAutomaticConversionRate: false,
          currencyConversionRate: selectedPo?.conversion_rate,
        };
      }

      if (defaultCurrency && !state.selectedCurrencyID) {
        return {
          ...state,
          selectedCurrencyID: defaultCurrency?.org_currency_id,
          selectedCurrencyName: defaultCurrency,
          currencyConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? defaultCurrency?.automatic_conversion_rate : defaultCurrency?.conversion_rate,
          isAutomaticConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
        };
      }
    }
    return state;
  }

  handleDelete = (key, id) => {
    const { grnTableData, selectedPoValue } = this.state;
    if (grnTableData.length > 1) {
      const copyData = grnTableData.filter((item) => item.key !== key || item.grn_line_id !== id);
      const updatedSelectedPoValue = selectedPoValue?.filter((item) =>
        copyData?.some((data) => data?.po_id === item?.value)
      );
      this.setState({ grnTableData: copyData, selectedPoValue: updatedSelectedPoValue }, () => {
        if (this.state?.isLineWiseDiscount) this.handleDiscountPercentageChange(this.state.discountPercentage);
      });
    } else {
      notification.error({
        message: 'You need to have at least one item in the GRN.',
        duration: 4,
        placement: 'top',
      });
    }
  }

  handleDeleteCharge = (chargeKey) => {
    const { chargeData } = this.state;
    const copyChargeData = chargeData.filter((item) => item.chargeKey !== chargeKey);
    this.setState({ chargeData: copyChargeData });
  }

  handleFullQuantity(item) {
    if (Number(Number(item?.quantity) - Number(item?.total_received_qty)) < 0) {
      // Do Nothing
    } else {
      const { grnTableData } = this.state;
      const copyData = JSON.parse(JSON.stringify(grnTableData));
      const updatedData = copyData.map((obj) => {
        if (!item.grn_line_id && (obj.po_line_id === item.po_line_id && item.po_line_id)) {
          const discountValue = Number(item?.discount) || 0;
          const totalPrice = ((Number(item?.quantity) - Number(item?.total_received_qty)) * item.offer_price);
          const taxableValue = item?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
            : Math.max(totalPrice - discountValue, 0);
          return {
            ...obj,
            received_qty: Number(item?.quantity) - Number(item?.total_received_qty),
            child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes,
          };
        }
        return obj;
      });
      this.setState({
        grnTableData: updatedData,
      });
    }
  }

  recordFullQuantity() {
    const { grnTableData } = this?.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    const updatedData = copyData.map((item) => {
      if (Number(Number(item?.quantity) - Number(item?.total_received_qty)) >= 0 && item.po_line_id) {
        const discountValue = Number(item?.discount) || 0;
        const totalPrice = ((Number(item?.quantity) - Number(item?.total_received_qty)) * item.offer_price);
        const taxableValue = item?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100) : Math.max(totalPrice - discountValue, 0);
        return {
          ...item,
          received_qty: QUANTITY(Number(item?.ordered_qty || item?.quantity) - Number(item?.total_received_qty || 0), item?.uom_info?.[0]?.precision),
          child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes,
        };
      }
      return item;
    })
    this.setState({
      grnTableData: updatedData,
    })
  }

  removeZeroQuantity() {
    const { grnTableData } = this?.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    const updatedData = copyData.filter((item) => (!((item.received_qty == 0) || (item?.received_qty == ''))));
    this.setState({
      grnTableData: updatedData,
    })
  }

  updateTableValue(key, value, label) {
    const { grnTableData } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    const updatedData = copyData.map((obj) => {
      if (obj.po_line_id === key && obj.po_line_id) {
        return {
          ...obj,
          [`${label}`]: value,
        };
      }
      return obj;
    });
    this.setState({
      grnTableData: updatedData,
    });
  }

  getClassName = (isFlexible, isOverflow, item) => {
    const { formSubmitted } = this.state;
    if (formSubmitted && (Number(item?.received_qty) <= 0)) {
      return 'orgFormInputError';
    }
    if (isFlexible) {
      return '';
    }
    if (formSubmitted && (Number(item?.quantity) - Number(item?.total_received_qty) - Number(item?.received_qty) < 0) || isOverflow) {
      return 'orgFormInputError';
    }

    return '';
  }

  handleCreateGRN = (approve) => {
    if (approve) {
      this.setState({ buttonClick: 'APPROVE', formSubmitted: true });
    } else {
      this.setState({ buttonClick: 'DRAFT', formSubmitted: true });
    }
    const {
      addGRN, history, match, selectedGRN, updateGRN, user, selectedPoForGrn, callback, getVendorRatingSuccess, createTag, taxesGroup,
    } = this.props;
    const {
      grnTableData, selectedPo, terms, grnDate, invoiceDate, invoiceNumber, fileList, tenantDepartmentId, formSubmitted, grnTypeValue, toRecipients, checkedRecipients,
      cfGoodReceivingNotesDoc, ewayBillList, ewayBillNumber, purchaseAccount, dueDate, chargeData, multipleBatchValidation, vendorAddress,
      discountPercentage, isLineWiseDiscount, selectedSeller, taxTypeInfo, taxType, selectedCurrencyName, taxTypeName, isMultipleBatchModeEnabled, charge1Name, charge1Value, isBatchValid, selectedTags, customVoucherNumber, freightTaxId, freightTaxInfo, transporterBillNumber, freightSacCode, transporterId, vehicleNumber, isAutomaticConversionRate, currencyConversionRate, gstNumber, selectedTenant, updateDocumentReason, narration, discountType, grnNumber, docSeqId, initialGrnNumber, paymentRemarks, paymentTerms,
    } = this.state;
    // validation for multiple batch mode
    if (isMultipleBatchModeEnabled) {
      function validateGrnTableData(grnTableDataTemp) {
        if (!Array.isArray(grnTableDataTemp)) return false;

        for (const item of grnTableDataTemp) {
          if (!Array.isArray(item.multiple_batch_info)) return false;

          for (const batch of item.multiple_batch_info) {
            if (!Array.isArray(batch.custom_fields)) return false;

            for (const field of batch.custom_fields) {
              if (field.isActive && field.isRequired) {
                const hasNoValue = field?.fieldValue === undefined || field?.fieldValue === null || field?.fieldValue === '';
                if (hasNoValue) return false; // Return false if a required field is missing a value
              }
            }
          }
        }
        return true; // Return true if all checks pass
      }
      const hasValidationErrors = grnTableData?.some((item) => item?.multiple_batch_info?.some(
        (batch) => !batch?.custom_batch_number || !batch?.quantity || Number(batch?.quantity) <= 0,
      ));

      if (hasValidationErrors || !validateGrnTableData(grnTableData)) {
        this.setState({ multipleBatchValidation: true });
        notification.open({
          message: 'Please enter all mandatory information',
          duration: 4,
          placement: 'top',
          type: 'error',
        });
        return;
      }
    }
    if (checkedRecipients && (!toRecipients || toRecipients.length === 0)) {
      notification.open({
        message: 'Please provide an email address for notification purposes.',
        duration: 4,
        placement: 'top',
        type: 'error',
      });
      return;
    }

    if (cfGoodReceivingNotesDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
      notification.open({
        message: 'Please fill all the mandatory inputs',
        duration: 4,
        placement: 'top',
        type: 'error',
      });
      return;
    }

    const { docLevelError, lineLevelError } = this.getGrnErrors();
    if (!!docLevelError?.length || lineLevelError?.length) {
      return;
    }

    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

    if (match?.params?.grnId) {
      const condition = selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE'
        ? this.isCreateValidDataADHOC(grnTableData)
        : this.isUpdateValidData(grnTableData);
      this.setState({ formSubmitted: true });
      if (condition && this.isDataValid2() && !cfGoodReceivingNotesDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length && (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory ? invoiceNumber : true)) {
        // creating payload for update case
        const grnLines = grnTableData?.map(
          (item) => {
            const lineQuantity = selectedGRN.grn_entity_type === 'GOOD_RECEIVING_NOTE' ? Number(item.quantity) : Number(item.received_qty);
            return {
              grn_entity_line_id: item.grn_entity_line_id || null,
              is_adhoc_line: !item.grn_entity_line_id,
              tenant_product_id: item.tenant_product_id || '',
              quantity: lineQuantity,
              invoice_quantity: item?.invoiceQuantity ?? lineQuantity,
              secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
              offer_price: Number(item.offer_price) || 0,
              tax_id: item?.taxId || '',
              tax_group_info: item?.taxInfo,
              uom_id: item?.uomId || '',
              uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
              expiry_date: item.expiryDate,
              line_discount_percentage: item?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : ((Number(item?.discount || 0) / (lineQuantity * parseFloat(item.offer_price))) * 100),
              is_discount_in_percent: item?.lineDiscountType === 'Percent',
              remarks: item.remarks,
              tally_purchase_account: item.tally_purchase_account,
              production_route_line_id: item?.production_route_line_id,
              custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
              po_line_status: item?.po_line_status?.map((item) => ({ line_status_received_qty: Number(item?.line_status_received_qty), po_line_status_id: item?.po_line_status_id, po_line_id: item?.po_line_id })),
              product_batches: isMultipleBatchModeEnabled ? (item.multiple_batch_info || [])?.map((batch) => ({
                ...batch,
                tenant_product_id: item.tenant_product_id || '',
                tenant_department_id: tenantDepartmentId,
                uom_id: item?.uomId || '',
                manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, item?.expiryDateFormat) : null,
                custom_fields: CustomFieldHelpers.postCfStructure(batch?.custom_fields),
              }))
                : (['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type) ? [] : [{
                  ...item?.selectedBatch,
                  quantity: selectedGRN.grn_entity_type === 'GOOD_RECEIVING_NOTE' ? Number(item.quantity) : Number(item.received_qty),
                  tenant_product_id: item.tenant_product_id || '',
                  tenant_department_id: tenantDepartmentId,
                  uom_id: item?.uomId || '',
                  mrp: Number(item?.selectedBatch?.mrp) || 0,
                  manufacturing_date: item?.selectedBatch?.manufacturing_date ? FormHelpers.dateFormatter(item?.selectedBatch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                  expiry_date: item?.selectedBatch?.expiry_date ? FormHelpers.dateFormatter(item?.selectedBatch?.expiry_date, item?.expiryDateFormat) : null,
                  custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
                }]),
            };
          }
          ,
        );
        if (grnLines.length > 0) {
          const payload = {
            tenant_seller_info: { ...selectedGRN?.tenant_seller_info, gst_number: gstNumber, seller_type: selectedSeller?.seller_type },
            tenant_department_id: selectedGRN?.tenant_department_id,
            notification_recipients: toRecipients,
            is_automatic_notification_enabled: checkedRecipients,
            grn_tenant_id: selectedTenant || selectedGRN?.grn_tenant_id,
            grn_id: selectedGRN?.grn_id,
            grn_lines: grnLines,
            status: approve ? 'ISSUED' : 'DRAFT',
            grn_date_time: grnDate,
            invoice_number: invoiceNumber || null,
            seller_id: selectedGRN?.seller_id || null,
            seller_address_info: vendorAddress,
            invoice_date: invoiceDate || null,
            tally_purchase_account: purchaseAccount,
            e_way_bill: ewayBillList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            e_way_bill_number: ewayBillNumber,
            grn_entity_type: selectedGRN?.grn_entity_type,
            grn_due_date: dueDate || null,
            custom_fields: CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc),
            other_charges: chargeData?.map((charge) => ({
              charge_name: charge?.charge_name,
              charge_amount: charge?.charge_amount,
              charge_type: '',
              charge_sac_code: charge?.chargesSacCode || null,
              tax_info: charge?.chargesTaxInfo || null,
              ledger_name: charge?.tallyLedgerName || null,
            })) || [],
            discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / (this.getLineTotals().totalAmount || 0)) * 100,
            is_discount_in_percent: discountType === 'Percent' ? true : false,
            is_line_wise_discount: !isLineWiseDiscount,
            attachments: fileList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            remark: terms || '',
            tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
            tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
              tax_id: taxTypeInfo?.tax_id,
              tax_name: taxTypeInfo?.tax_name,
              tax_value: taxTypeInfo?.tax_value,
              tax_type_name: taxTypeInfo?.tax_type_name,
            } : null,
            tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
            tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
              tax_id: taxTypeInfo?.tax_id,
              tax_name: taxTypeInfo?.tax_name,
              tax_value: taxTypeInfo?.tax_value,
              tax_type_name: taxTypeInfo?.tax_type_name,
            } : null,
            conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
            org_currency_id: selectedCurrencyName?.org_currency_id,
            charge_1_name: charge1Name,
            charge_1_value: Number(charge1Value),
            transporter_id: transporterId,
            vehicle_number: vehicleNumber,
            freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
            freight_tax_info: freightTaxInfo,
            freight_sac_code: freightSacCode,
            transporter_bill_number: transporterBillNumber,
            update_document_reason: updateDocumentReason,
            narration: narration || '',
            grn_number: initialGrnNumber?.toLowerCase()?.trim() === grnNumber?.toLowerCase()?.trim() ? null : grnNumber,
            allow_grn_to_create_ap_invoice: isApInvoiceEnabled,
            payment_terms: [
              {
                advance_amount: 0,
                due_days: paymentTerms || 0,
                remark: paymentRemarks || '',
              },
            ],
          };
          updateGRN(payload, () => {
            this.setState({ formSubmitted: false });
            if (selectedTags?.length) {
              const tagPayload = {
                entity_name: 'GRN',
                entity_id: selectedGRN?.grn_id,
                action: 'REPLACE',
                tag: selectedTags.join(','),
              };
              createTag(tagPayload, () => {
                if (approve && getVendorRatingSuccess?.is_active) {
                  this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${selectedGRN?.grn_id}` });
                } else {
                  history.push(`/purchase/goods-receiving/view/${selectedGRN?.grn_id}`);
                }
              });
            } else if (approve && getVendorRatingSuccess?.is_active) {
              this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${selectedGRN?.grn_id}` });
            } else {
              history.push(`/purchase/goods-receiving/view/${selectedGRN?.grn_id}`);
            }
          });
        }
      }
    } else {
      // this case for grn from po screen
      this.setState({ formSubmitted: true });
      if (selectedPoForGrn?.po_id && this.isCreateValidData(grnTableData) && this.isDataValid2() && isBatchValid && grnTableData?.filter((item) => Number(item.received_qty))?.length > 0 && !cfGoodReceivingNotesDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
        const grnLines = grnTableData?.filter((item) => Number(item.received_qty))?.map(
          (item) => ({
            mo_fg_id: item?.mo_fg_id,
            mo_line_id: item?.mo_line_id,
            grn_entity_line_id: item.po_line_id || null,
            is_adhoc_line: !item.po_line_id,
            tenant_product_id: item.tenant_product_id || '',
            quantity: item.received_qty ? Number(item.received_qty) : 0,
            invoice_quantity: item?.invoiceQuantity ?? Number(item?.received_qty ?? 0),
            secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
            offer_price: Number(item.offer_price) || 0,
            tax_id: item?.taxId || '',
            tax_group_info: item?.taxInfo,
            remarks: item?.remarks,
            line_discount_percentage: item?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : ((Number(item?.discount || 0) / (Number(item.received_qty) * parseFloat(item.offer_price))) * 100),
            is_discount_in_percent: item?.lineDiscountType === 'Percent',
            uom_id: item?.uomId || '',
            uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
            expiry_date: item.expiryDate,
            tally_purchase_account: item.tally_purchase_account,
            production_route_line_id: item?.production_route_line_id,
            custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
            product_sku_id: item?.product_sku_id,
            product_batches: isMultipleBatchModeEnabled
              ? (item.multiple_batch_info || []).map((batch) => ({
                ...batch,
                tenant_product_id: item.tenant_product_id || '',
                tenant_department_id: tenantDepartmentId,
                uom_id: item?.uomId || '',
                manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, item?.expiryDateFormat) : null,
                custom_fields: CustomFieldHelpers.postCfStructure(batch?.custom_fields),
              }))
              : (['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type)
                ? [] // If product type is 'NON_STORABLE' or 'SERVICE', don't send any batches
                : [{
                  ...item?.selectedBatch,
                  quantity: Number(item.received_qty),
                  tenant_product_id: item.tenant_product_id || '',
                  tenant_department_id: tenantDepartmentId,
                  uom_id: item?.uomId || '',
                  mrp: Number(item?.selectedBatch?.mrp) || 0,
                  manufacturing_date: item?.selectedBatch?.manufacturing_date ? FormHelpers.dateFormatter(item?.selectedBatch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                  expiry_date: item?.selectedBatch?.expiry_date ? FormHelpers.dateFormatter(item?.selectedBatch?.expiry_date, item?.expiryDateFormat) : null,
                  custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
                }]),
            po_line_status: item?.po_line_status?.map((item) => ({ line_status_received_qty: Number(item?.line_status_received_qty), po_line_status_id: item?.po_line_status_id, po_line_id: item?.po_line_id })),
          }),
        );

        const payload = {
          grn_lines: grnLines?.filter((item) => item),
          notification_recipients: toRecipients,
          is_automatic_notification_enabled: checkedRecipients,
          grn_entity_id: selectedPoForGrn?.po_id,
          seller_id: selectedPoForGrn?.tenant_seller_info?.seller_id,
          tenant_department_id: tenantDepartmentId,
          tenant_seller_info: { ...selectedPoForGrn?.tenant_seller_info, internal_slr_code: selectedPoForGrn?.seller_info?.internal_slr_code, gst_number: gstNumber },
          seller_address_info: selectedPoForGrn?.seller_address,
          tally_purchase_account: purchaseAccount,
          grn_date_time: grnDate,
          invoice_number: invoiceNumber || null,
          e_way_bill: ewayBillList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
          })) || [],
          e_way_bill_number: ewayBillNumber,
          invoice_date: invoiceDate,
          status: (grnLines?.filter((item) => item).length > 0) && (approve ? 'ISSUED' : 'DRAFT'),
          grn_tenant_id: selectedTenant || selectedPoForGrn?.tenant_id,
          grn_entity_type: 'PURCHASE_ORDER',
          remark: terms,
          grn_due_date: dueDate || null,
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
          })) || [],
          custom_fields: CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc),
          other_charges: chargeData?.map((charge) => ({
            charge_name: charge?.charge_name,
            charge_amount: charge?.charge_amount,
            charge_type: '',
            charge_sac_code: charge?.chargesSacCode || null,
            tax_info: charge?.chargesTaxInfo || null,
            ledger_name: charge?.tallyLedgerName || null,
          })) || [],
          discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / (this.getLineTotals().totalAmount || 0)) * 100,
          is_discount_in_percent: discountType === 'Percent' ? true : false,
          is_line_wise_discount: !isLineWiseDiscount,
          tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
          tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
            tax_id: taxTypeInfo?.tax_id,
            tax_name: taxTypeInfo?.tax_name,
            tax_value: taxTypeInfo?.tax_value,
            tax_type_name: taxTypeInfo?.tax_type_name,
          } : null,
          tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
          tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
            tax_id: taxTypeInfo?.tax_id,
            tax_name: taxTypeInfo?.tax_name,
            tax_value: taxTypeInfo?.tax_value,
            tax_type_name: taxTypeInfo?.tax_type_name,
          } : null,
          conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
          org_currency_id: selectedCurrencyName?.org_currency_id,
          charge_1_name: charge1Name,
          charge_1_value: isApInvoiceEnabled ? 0 : Number(charge1Value),
          transporter_id: transporterId,
          vehicle_number: vehicleNumber,
          freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
          freight_tax_info: freightTaxInfo,
          freight_sac_code: freightSacCode,
          transporter_bill_number: transporterBillNumber,
          is_job_works_grn: selectedPoForGrn?.is_job_works_po,
          production_route_id: selectedPoForGrn?.production_route_id,
          product_sku_id: selectedPoForGrn?.product_sku_id,
          narration: narration || '',
          grn_number: initialGrnNumber?.toLowerCase()?.trim() === grnNumber?.toLowerCase()?.trim() ? null : grnNumber,
          seq_id: docSeqId || null,
          allow_grn_to_create_ap_invoice: isApInvoiceEnabled,
          payment_terms: [
            {
              advance_amount: 0,
              due_days: paymentTerms || 0,
              remark: paymentRemarks || '',
            },
          ],
        };
        addGRN(payload, (grn) => {
          if (selectedTags?.length) {
            const tagPayload = {
              entity_name: 'GRN',
              entity_id: grn?.good_receiving_note?.grn_id,
              action: 'ADD',
              tag: selectedTags.join(','),
            };
            createTag(tagPayload, () => {
              if (callback) {
                if (approve && getVendorRatingSuccess?.is_active) {
                  this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`, latestGrnId: grn?.good_receiving_note?.grn_id });
                } else {
                  callback();
                }
              }
            });
          } else if (callback) {
            if (approve && getVendorRatingSuccess?.is_active) {
              this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`, latestGrnId: grn?.good_receiving_note?.grn_id });
            } else {
              callback();
            }
          }
        });
      }

      if (!selectedPoForGrn?.po_id && this.isCreateValidData(grnTableData) && this.isDataValid2() && isBatchValid && grnTableData?.filter((item) => Number(item.received_qty))?.length > 0 && !cfGoodReceivingNotesDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
        // this case for grn by selecting po in grn form
        const grnLines = grnTableData?.filter((item) => Number(item.received_qty))?.map(
          (item) => ({
            grn_entity_line_id: item.po_line_id || null,
            is_adhoc_line: !item.po_line_id,
            tenant_product_id: item.tenant_product_id || '',
            quantity: item.received_qty ? Number(item.received_qty) : 0,
            invoice_quantity: item?.invoiceQuantity ?? Number(item?.received_qty ?? 0),
            secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
            offer_price: Number(item.offer_price) || 0,
            remarks: item.remarks || '',
            tax_id: item?.tax_id || item?.taxId,
            tax_group_info: item?.taxInfo,
            uom_id: item?.uom_id || item?.uomId,
            uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
            expiry_date: item.expiryDate,
            line_discount_percentage: item?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : ((Number(item?.discount || 0) / (Number(item.received_qty) * parseFloat(item.offer_price))) * 100),
            is_discount_in_percent: item?.lineDiscountType === 'Percent',
            tally_purchase_account: item.tally_purchase_account,
            production_route_line_id: item?.production_route_line_id,
            custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
            product_batches: isMultipleBatchModeEnabled
              ? (item.multiple_batch_info || []).map((batch) => ({
                ...batch,
                tenant_product_id: item.tenant_product_id || '',
                tenant_department_id: tenantDepartmentId,
                uom_id: item?.uomId || '',
                manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, item?.expiryDateFormat) : null,
                custom_fields: CustomFieldHelpers.postCfStructure(batch?.custom_fields),
              }))
              : ['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type)
                ? [] // If product type is 'NON_STORABLE' or 'SERVICE', don't send any batches
                : [{
                  ...item?.selectedBatch,
                  quantity: Number(item.received_qty),
                  tenant_product_id: item.tenant_product_id || '',
                  tenant_department_id: tenantDepartmentId,
                  uom_id: item?.uomId || '',
                  mrp: Number(item?.selectedBatch?.mrp) || 0,
                  manufacturing_date: item?.selectedBatch?.manufacturing_date ? FormHelpers.dateFormatter(item?.selectedBatch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                  expiry_date: item?.selectedBatch?.expiry_date ? FormHelpers.dateFormatter(item?.selectedBatch?.expiry_date, item?.expiryDateFormat) : null,
                  custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
                }],
          }),
        );
        if (grnLines?.length) {
          const payload = {
            grn_lines: grnLines,
            notification_recipients: toRecipients,
            is_automatic_notification_enabled: checkedRecipients,
            tenant_seller_info: grnTypeValue === 'MULTIPO' ? {
              seller_id: selectedSeller?.seller_info?.seller_id,
              seller_name: selectedSeller?.seller_info?.seller_name,
              tenant_seller_id: selectedSeller?.tenant_seller_id,
              gst_number: gstNumber,
              seller_type: selectedSeller?.seller_info?.seller_type,
            } : { ...selectedPo?.tenant_seller_info, internal_slr_code: selectedGRN?.internal_slr_code, gst_number: gstNumber },
            seller_address_info: grnTypeValue === 'MULTIPO' ? vendorAddress : null,
            tenant_department_id: grnTypeValue === 'MULTIPO' ? tenantDepartmentId : selectedPo?.tenant_department_id,
            grn_entity_id: selectedPo?.po_id,
            grn_date_time: grnDate,
            invoice_date: invoiceDate,
            invoice_number: invoiceNumber || null,
            seller_id: selectedSeller?.seller_info?.seller_id || null,
            status: (grnLines.filter((item) => item).length > 0) && (approve ? 'ISSUED' : 'DRAFT'),
            grn_tenant_id: selectedTenant || selectedPo?.tenant_id,
            grn_entity_type: 'PURCHASE_ORDER',
            remark: terms,
            tally_purchase_account: purchaseAccount,
            grn_due_date: dueDate || null,
            attachments: fileList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            custom_fields: CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc),
            other_charges: chargeData?.map((charge) => ({
              charge_name: charge?.charge_name,
              charge_amount: charge?.charge_amount,
              charge_type: '',
              charge_sac_code: charge?.chargesSacCode || null,
              tax_info: charge?.chargesTaxInfo || null,
              ledger_name: charge?.tallyLedgerName || null,
            })) || [],
            discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / (this.getLineTotals().totalAmount || 0)) * 100,
            is_line_wise_discount: !isLineWiseDiscount,
            tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
            tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
              tax_id: taxTypeInfo?.tax_id,
              tax_name: taxTypeInfo?.tax_name,
              tax_value: taxTypeInfo?.tax_value,
              tax_type_name: taxTypeInfo?.tax_type_name,
            } : null,
            tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
            tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
              tax_id: taxTypeInfo?.tax_id,
              tax_name: taxTypeInfo?.tax_name,
              tax_value: taxTypeInfo?.tax_value,
              tax_type_name: taxTypeInfo?.tax_type_name,
            } : null,
            conversion_rate: grnTypeValue === 'MULTIPO' ? selectedSeller?.currency_info?.conversion_rate : selectedPo?.conversion_rate,
            org_currency_id: selectedCurrencyName?.org_currency_id,
            charge_1_name: charge1Name,
            charge_1_value: Number(charge1Value),
            transporter_id: transporterId,
            vehicle_number: vehicleNumber,
            freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
            freight_tax_info: freightTaxInfo,
            freight_sac_code: freightSacCode,
            transporter_bill_number: transporterBillNumber,
            e_way_bill: ewayBillList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            e_way_bill_number: ewayBillNumber,
            narration: narration || '',
            grn_number: initialGrnNumber?.toLowerCase()?.trim() === grnNumber?.toLowerCase()?.trim() ? null : grnNumber,
            seq_id: docSeqId || null,
            allow_grn_to_create_ap_invoice: isApInvoiceEnabled,
            seller_type: grnTypeValue === 'MULTIPO' ? selectedSeller?.seller_type : selectedPo?.tenant_seller_info?.seller_type,
            payment_terms: [
              {
                advance_amount: 0,
                due_days: paymentTerms || 0,
                remark: paymentRemarks || '',
              },
            ],
          };

          addGRN(payload, (grn) => {
            if (selectedTags?.length) {
              const tagPayload = {
                entity_name: 'GRN',
                entity_id: grn?.good_receiving_note?.grn_id,
                action: 'ADD',
                tag: selectedTags.join(','),
              };
              createTag(tagPayload, () => {
                if (approve && getVendorRatingSuccess?.is_active) {
                  this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`, latestGrnId: grn?.good_receiving_note?.grn_id });
                } else {
                  history.push(`/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`);
                }
              });
            } else if (approve && getVendorRatingSuccess?.is_active) {
              this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`, latestGrnId: grn?.good_receiving_note?.grn_id });
            } else {
              history.push(`/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`);
            }
          });
        }
      } else
        // this case for creating adhoc grn
        if (grnTypeValue === 'ADHOC' && this.isCreateValidDataADHOC(grnTableData) && this.isDataValid2() && isBatchValid && grnTableData?.map((item) => Number(item.quantity))?.length > 0 && !cfGoodReceivingNotesDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
          const grnLines = grnTableData?.map(
            (item) => ({
              grn_entity_line_id: item.po_line_id || null,
              is_adhoc_line: !item.po_line_id,
              tenant_product_id: item.tenant_product_id || '',
              quantity: grnTypeValue === 'ADHOC' ? item.quantity : Number(item.received_qty),
              invoice_quantity: item?.invoiceQuantity ?? grnTypeValue === 'ADHOC' ? Number(item?.quantity) : Number(item.received_qty),
              secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
              offer_price: Number(item.offer_price) || 0,
              remarks: item.remarks || '',
              tax_id: item?.taxId || '',
              tax_group_info: item?.taxInfo,
              uom_id: item?.uomId || '',
              uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
              expiry_date: item.expiryDate || null,
              line_discount_percentage: item?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : ((Number(item?.discount || 0) / (Number(item.quantity) * parseFloat(item.offer_price))) * 100),
              is_discount_in_percent: item?.lineDiscountType === 'Percent',
              tally_purchase_account: item.tally_purchase_account,
              production_route_line_id: item?.production_route_line_id,
              custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
              product_batches: isMultipleBatchModeEnabled
                ? (item.multiple_batch_info || []).map((batch) => ({
                  ...batch,
                  manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                  expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, item?.expiryDateFormat) : null,
                  tenant_product_id: item.tenant_product_id || '',
                  tenant_department_id: tenantDepartmentId,
                  uom_id: item?.uomId || '',
                  custom_fields: CustomFieldHelpers.postCfStructure(batch?.custom_fields),
                }))
                : ['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type)
                  ? [] // If product type is 'NON_STORABLE' or 'SERVICE', don't send any batches
                  : [{
                    ...item?.selectedBatch,
                    quantity: item.quantity ? Number(item.quantity) : 0,
                    tenant_product_id: item.tenant_product_id || '',
                    tenant_department_id: tenantDepartmentId,
                    uom_id: item?.uomId || '',
                    mrp: Number(item?.selectedBatch?.mrp) || 0,
                    manufacturing_date: item?.selectedBatch?.manufacturing_date ? FormHelpers.dateFormatter(item?.selectedBatch?.manufacturing_date, item?.manufacturingDateFormat) : null,
                    expiry_date: item?.selectedBatch?.expiry_date ? FormHelpers.dateFormatter(item?.selectedBatch?.expiry_date, item?.expiryDateFormat) : null,
                    custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
                  }],
            }),
          );
          if (grnLines?.length) {
            const payload = {
              grn_lines: grnLines.filter((item) => item),
              tenant_seller_info: {
                tenant_seller_id: selectedSeller?.tenant_seller_id,
                gst_number: gstNumber,
                ...selectedSeller?.seller_info,
                seller_type: selectedSeller?.seller_info?.seller_type,
              },
              notification_recipients: toRecipients,
              is_automatic_notification_enabled: checkedRecipients,
              tenant_department_id: tenantDepartmentId,
              grn_entity_id: null,
              notification_recipients: toRecipients,
              is_automatic_notification_enabled: checkedRecipients,
              grn_date_time: grnDate,
              invoice_date: invoiceDate,
              invoice_number: invoiceNumber || null,
              seller_id: selectedSeller?.seller_info?.seller_id || null,
              seller_address_info: vendorAddress,
              tally_purchase_account: purchaseAccount,
              status: (grnLines.filter((item) => item).length > 0) && (approve ? 'ISSUED' : 'DRAFT'),
              grn_tenant_id: selectedTenant,
              grn_entity_type: 'GOOD_RECEIVING_NOTE',
              remark: terms,
              grn_due_date: dueDate || null,
              attachments: fileList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              custom_fields: CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc),
              other_charges: chargeData?.map((charge) => ({
                charge_name: charge?.charge_name,
                charge_amount: charge?.charge_amount,
                charge_type: '',
                charge_sac_code: charge?.chargesSacCode || null,
                tax_info: charge?.chargesTaxInfo || null,
                ledger_name: charge?.tallyLedgerName || null,
              })) || [],
              discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / (this.getLineTotals().totalAmount || 0)) * 100,
              is_discount_in_percent: discountType === 'Percent' ? true : false,
              is_line_wise_discount: !isLineWiseDiscount,
              tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
              tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              } : null,
              tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
              tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              } : null,
              conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
              org_currency_id: selectedCurrencyName?.org_currency_id,
              charge_1_name: charge1Name,
              charge_1_value: Number(charge1Value),
              transporter_id: transporterId,
              vehicle_number: vehicleNumber,
              freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
              freight_tax_info: freightTaxInfo,
              freight_sac_code: freightSacCode,
              transporter_bill_number: transporterBillNumber,
              e_way_bill: ewayBillList?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
              })) || [],
              e_way_bill_number: ewayBillNumber,
              narration: narration || '',
              grn_number: initialGrnNumber?.toLowerCase()?.trim() === grnNumber?.toLowerCase()?.trim() ? null : grnNumber,
              seq_id: docSeqId || null,
              allow_grn_to_create_ap_invoice: isApInvoiceEnabled,
              seller_type: selectedSeller?.seller_type,
              payment_terms: [
                {
                  advance_amount: 0,
                  due_days: paymentTerms || 0,
                  remark: paymentRemarks || '',
                },
              ],
            };
            addGRN(payload, (grn) => {
              if (selectedTags?.length) {
                const tagPayload = {
                  entity_name: 'GRN',
                  entity_id: grn?.good_receiving_note?.grn_id,
                  action: 'ADD',
                  tag: selectedTags.join(','),
                };
                createTag(tagPayload, () => {
                  if (approve && getVendorRatingSuccess?.is_active) {
                    this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`, latestGrnId: grn?.good_receiving_note?.grn_id });
                  } else {
                    history.push(`/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`);
                  }
                });
              } else if (approve && getVendorRatingSuccess?.is_active) {
                this.setState({ showModal: true, getHistory: `/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`, latestGrnId: grn?.good_receiving_note?.grn_id });
              } else {
                history.push(`/purchase/goods-receiving/view/${grn?.good_receiving_note?.grn_id}`);
              }
            });
          }
        }
    }
  }

  getLineTotals(grnTableData, chargeData, charge1Value) {
    const {
      grnTypeValue, taxTypeInfo, taxTypeName, freightTax, freightTaxData,
    } = this.state;
    grnTableData = grnTableData ?? this.state.grnTableData;
    chargeData = chargeData ?? this.state.chargeData;
    charge1Value = charge1Value ?? this.state.charge1Value;
    let totalAmount = 0;
    let totalDiscount = 0;
    let totalBase = 0;
    let totalTcs = 0;
    let totalTds = 0;
    let totalTax = 0;
    let totalOtherCharge = 0;
    let totalOtherChargeWithOutTax = 0;
    let grnTotal = 0;
    let totalTaxValue = 0;
    let freightCharge = Number(charge1Value);
    let totalOtherChargesForTaxableAmount = 0;
    let freightTaxAmount = 0;

    // Taxes Bifurcation
    if (grnTableData?.length > 0) {
      totalTaxValue = Helpers.groupAndSumByTaxName(grnTableData?.map((item) => item?.child_taxes)?.flat())?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
    }

    for (let i = 0; i < chargeData?.length; i++) {
      let currentOtherCharge = 0;
      let currentOtherChargeWithoutTax = 0;
      let chargesTaxAmountLinesWise = 0;
      if (chargeData[i]?.charge_amount) {
        chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName([...chargeData[i]?.chargesTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
        currentOtherCharge = chargeData?.[i]?.chargesTax ? chargeData?.[i]?.charge_amount + (chargesTaxAmountLinesWise) : chargeData?.[i]?.charge_amount;
        currentOtherChargeWithoutTax = chargeData?.[i]?.charge_amount;
        if (chargeData?.[i]?.chargesTax) {
          totalOtherChargesForTaxableAmount += chargeData?.[i]?.charge_amount;
        }
      }
      totalOtherCharge += currentOtherCharge;
      totalOtherChargeWithOutTax += currentOtherChargeWithoutTax;
    }
    let lines = [];
    for (let i = 0; i < grnTableData?.length; i++) {
      let currentAmount = 0;
      let currentDiscount = 0;
      let currentBase = 0;
      let currentTax = 0;
      let currentGRN = 0;

      const quantityToUse = grnTypeValue === 'ADHOC' ? grnTableData[i]?.quantity : grnTableData[i]?.received_qty;

      const discountValue = Number(grnTableData[i].discount);

      if (quantityToUse) {
        currentAmount += (quantityToUse * grnTableData[i].offer_price);

        if (discountValue) {
          if (grnTableData[i]?.lineDiscountType === 'Percent') {
            currentDiscount += (quantityToUse * grnTableData[i].offer_price) * (discountValue / 100);

            currentBase = (quantityToUse * grnTableData[i].offer_price) * Number(100 - discountValue) / 100;
          } else if (grnTableData[i]?.lineDiscountType === 'Amount') {
            currentDiscount += discountValue;
            currentBase += (quantityToUse * grnTableData[i].offer_price) - discountValue;
          }
        } else {
          currentDiscount += 0;
          currentBase += (quantityToUse * grnTableData[i].offer_price);
        }
      }
      // Subtract TDS from currentBase or Add TCS
      if (taxTypeInfo && taxTypeName === 'TCS') {
        const tcsRate = taxTypeInfo?.tax_value / 100;
        const tcsAmount = currentBase * tcsRate;
        totalTcs += tcsAmount;
      } else if (taxTypeInfo && taxTypeName === 'TDS') {
        const tdsRate = taxTypeInfo?.tax_value / 100;
        const tdsAmount = currentBase * tdsRate;
        totalTds -= tdsAmount;
      }

      if (((grnTableData[i]?.taxInfo?.tax_value) || (grnTableData[i]?.taxInfo?.[0]?.tax_value))) currentTax = currentBase * (((grnTableData[i]?.taxInfo?.tax_value) || (grnTableData[i]?.taxInfo?.[0]?.tax_value)) / 100);
      if (currentBase) currentGRN = currentBase;

      totalAmount += currentAmount;
      totalDiscount += currentDiscount;
      totalBase += currentBase;
      totalTax += currentTax;
      grnTotal += currentGRN;
      lines.push({
        grn_line_total: currentGRN + currentTax + (taxTypeName === 'TCS' ? totalTcs : totalTds),
        grn_line_quantity: quantityToUse,
        grn_line_unit_price: grnTableData[i].offer_price,
      });
    }
    grnTotal += totalTaxValue;
    lines = lines.map((line) => {
      let unitOtherCharge = 0;
      let unitFreightCharge = 0;
      const unitCostPrice = Number(line.grn_line_unit_price);
      unitOtherCharge = Number(((line.grn_line_total / grnTotal) * totalOtherChargeWithOutTax) / line.grn_line_quantity);
      unitFreightCharge = Number(((line.grn_line_total / grnTotal) * Number(charge1Value)) / line.grn_line_quantity);
      line.unit_landed_cost = Number(unitCostPrice + unitOtherCharge + unitFreightCharge).toFixed(2) || 0;
      line.unit_other_cost = Number(unitOtherCharge).toFixed(2) || 0;
      line.unit_freight_cost = Number(unitFreightCharge).toFixed(2) || 0;
      return line;
    });

    if (freightTax) {
      freightTaxAmount = Helpers.groupAndSumByTaxName([...freightTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

      totalBase += freightCharge;
      freightCharge += freightTaxAmount;
      // Subtract TDS from currentBase or Add TCS
      if (taxTypeInfo && taxTypeName === 'TCS') {
        const tcsRate = taxTypeInfo?.tax_value / 100;
        totalTcs = totalBase * tcsRate;
      } else if (taxTypeInfo && taxTypeName === 'TDS') {
        const tdsRate = taxTypeInfo?.tax_value / 100;
        totalTds = -((totalBase + totalOtherChargesForTaxableAmount) * tdsRate);
      }
    }

    // Add TCS from to total amount including GST ,Freight Other charges
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;

      //If we have freight tax then add only freight tax amount as freight charge is already included in totalBase or if freight tax  is not present add freightCharge
      const tcsAmount = (totalBase + totalTaxValue + Number(totalOtherCharge) + (freightTax ? freightTaxAmount : freightCharge)) * tcsRate;
      totalTcs = tcsAmount;
    }

    grnTotal += (taxTypeName === 'TCS' ? totalTcs : totalTds);
    grnTotal += totalOtherCharge;
    grnTotal += freightCharge;
    totalBase += totalOtherChargesForTaxableAmount;
    return {
      totalAmount,
      totalDiscount,
      totalBase,
      totalTax,
      totalTcs,
      totalTds,
      grnTotal,
      lines,
    };
  }

  isUpdateValidData(grnData) {
    let isValid = true;
    const {
      grnDate, ewayBillNumber, ewayBillList, purchaseAccount, invoiceNumber, selectedPo, selectedTenant,
    } = this.state;
    const { user, selectedPoForGrn } = this.props;

    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

    for (let i = 0; i < grnData?.length; i++) {
      const item = grnData[i];
      const quantity = Number(item?.quantity);
      const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent);
      const overFlowQuantity = ((grnPercent / 100) * quantity) + quantity;

      function checkOverflow() {
        if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) {
          if (grnPercent > 0) {
            if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
              return true;
            }
          } else if (grnPercent === 0 || grnPercent === null) {
            if ((overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
              return false;
            }
          }
        } else if (grnPercent > 0) {
          if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
            return true;
          }
        } else if (grnPercent === 0 || grnPercent === null) {
          if ((quantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
            return true;
          }
        }
      }

      if ((checkOverflow()
        || Number(item.received_qty) <= 0 || (isApInvoiceEnabled ? !item?.offer_price && item?.offer_price !== 0 : Number(item.offer_price) <= 0)) || !grnDate
        || ((user?.user_tenants?.find((k) => k?.tenant_id === (selectedTenant || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id || user?.tenant_info?.tenant_id))?.tally_connection_status && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) ? !item?.tally_purchase_account : false) || !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
      ) {
        isValid = false;
        break;
      }
    }
    if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory) {
      if (this.getLineTotals().totalAmount >= 50000 && user?.tenant_info?.country_code === 'IN') {
        if (!ewayBillNumber || !ewayBillList?.length) {
          isValid = false;
        }
      }
    }
    if (!purchaseAccount && user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled) {
      isValid = false;
    }
    if ((user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory && !invoiceNumber)) {
      isValid = false;
    }
    return isValid;
  }

  isCreateValidData(grnData) {
    let isValid = true;
    const {
      grnDate, ewayBillNumber, ewayBillList, purchaseAccount, invoiceNumber, grnTypeValue, selectedPo, selectedTenant,
    } = this.state;
    const { user, selectedPoForGrn } = this.props;

    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
    if (!grnDate) return false;
    if (grnTypeValue === 'ADHOC') {
      return !isValid;
    }
    for (let i = 0; i < grnData?.length; i++) {
      const item = grnData[i];
      const quantity = Number(item?.quantity);
      const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent || 0);
      const overFlowQuantity = Math.floor(((grnPercent / 100) * quantity) + quantity);

      function checkOverflow() {
        if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) {
          if (grnPercent > 0) {
            if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
              return true;
            }
          } else if (grnPercent === 0 || grnPercent === null) {
            if ((overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
              return false;
            }
          }
        } else if (grnPercent > 0) {
          if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
            return true;
          }
        } else if (grnPercent === 0 || grnPercent === null) {
          if ((quantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
            return true;
          }
        }
      }

      if ((checkOverflow()
        || Number(item.received_qty) <= 0 || (isApInvoiceEnabled ? !item?.offer_price && item?.offer_price !== 0 : Number(item.offer_price) <= 0)) || !grnDate
        || ((user?.user_tenants?.find((k) => k?.tenant_id === (selectedTenant || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id))?.tally_connection_status && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) ? !item?.tally_purchase_account : false) || !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
      ) {
        isValid = false;
        break;
      }
      if (item?.product_sku_info?.product_type === 'STORABLE' && !item?.selectedBatch) {
        isValid = false;
      }
    }
    if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory) {
      if (this.getLineTotals().totalAmount >= 50000 && user?.tenant_info?.country_code === 'IN') {
        if (!ewayBillNumber || !ewayBillList?.length) {
          isValid = false;
        }
      }
    }
    if (!purchaseAccount && user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled) {
      isValid = false;
    }
    if ((user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory && !invoiceNumber)) {
      isValid = false;
    }
    const { docLevelError, lineLevelError } = this.getGrnErrors();
    if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
      return false;
    }
    return isValid;
  }

  isCreateValidDataADHOC(grnData) {
    let isValid = true;
    const {
      grnDate, purchaseAccount, invoiceNumber, selectedSellerId, selectedSeller, grnTypeValue, vendorAddress, ewayBillNumber, ewayBillList,
    } = this.state;
    const { user, selectedGRN, selectedTenant } = this.props;

    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
    if ((!selectedSeller?.seller_id && !selectedSellerId) || !vendorAddress) {
      isValid = false;
    }
    if (!grnDate) return false;
    for (let i = 0; i < grnData?.length; i++) {
      const item = grnData[i];
      if ((Number(item.received_qty) <= 0 || (isApInvoiceEnabled ? !item?.offer_price && item?.offer_price !== 0 : Number(item.offer_price) <= 0)) || !grnDate
        || ((user?.user_tenants?.find((k) => k?.tenant_id === (selectedTenant))?.tally_connection_status && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) ? !item?.tally_purchase_account : false) || !CustomFieldHelpers.isCfValid(item?.lineCustomFields)
      ) {
        isValid = false;
        break;
      }
      if (item?.product_sku_info?.product_type === 'STORABLE' && !item?.selectedBatch) {
        isValid = false;
      }
    }
    if (!purchaseAccount && user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled) {
      isValid = false;
    }
    if ((user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory && !invoiceNumber)) {
      isValid = false;
    }
    if ((user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory && !invoiceNumber) && (grnTypeValue === 'ADHOC' || selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE')) {
      isValid = false;
    }
    if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory) {
      if (this.getLineTotals().totalAmount >= 50000 && user?.tenant_info?.country_code === 'IN') {
        if (!ewayBillNumber || !ewayBillList?.length) {
          isValid = false;
        }
      }
    }
    const { docLevelError, lineLevelError } = this.getGrnErrors();
    if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
      return false;
    }

    return isValid;
  }

  isDataValid2() {
    const { chargeData } = this.state;
    let isDataValid = true;
    if (chargeData?.length) {
      for (let i = 0; i < chargeData?.length; i++) {
        if (!chargeData[i].charge_name || !chargeData[i].charge_amount) {
          isDataValid = false;
        }
      }
    }

    return isDataValid;
  }

  customInputChange(fieldValue, cfId) {
    const { cfGoodReceivingNotesDoc } = this.state;
    const newCustomField = cfGoodReceivingNotesDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfGoodReceivingNotesDoc: newCustomField,
    });
  }

  // 🔹 Single reusable function to evaluate dependent fields (with expression handling included)
  evaluateDependentFields = (cf, cfMap) => {
    if (!cf?.dependentFields?.length) return;

    for (const dependentFieldId of cf.dependentFields) {
      const dependentField = cfMap[dependentFieldId];
      if (dependentField?.defaultExpression) {
        try {
          // inline expression evaluator
          const expression = dependentField.defaultExpression;
          const evaluatedExpression = expression.replaceAll(/{{cf_(\d+)}}/g, (match, p1) => {
            const field = cfMap[p1];
            if (!field) return 0;
            return Number(field.fieldValue || 0);
          });

          const evaluatedValue = eval(evaluatedExpression);
          dependentField.fieldValue = evaluatedValue;

          // 🔁 recursive evaluation for chained dependencies
          this.evaluateDependentFields(dependentField, cfMap);
        } catch (error) {
          console.error(
            `Failed to evaluate expression for field ${dependentFieldId}:`,
            error
          );
        }
      }
    }
  };

  customLineInputChange(fieldValue, cfId, record, returnCfData = false) {
    const { grnTableData, discountPercentage, isLineWiseDiscount, grnTypeValue } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));

    let updatedLineCfs = null; // to hold updated lineCustomFields

    for (const item of copyData) {
      if (
        (record.po_line_id === item.po_line_id && item.po_line_id) ||
        (record.grn_line_id === item?.grn_line_id && item?.grn_line_id) ||
        (record.key === item?.key && item?.key)
      ) {
        const copylineCfs = item?.lineCustomFields;
        const cfMap = {};
        if (copylineCfs) {
          for (const cf of copylineCfs) {
            cfMap[cf.cfId] = cf; // build map for dependency evaluation
          }
        }

        for (const cf of copylineCfs) {
          if (cf.cfId === cfId) {
            // update current field
            cf.fieldValue =
              cf.fieldType === "ATTACHMENT"
                ? fieldValue?.map((attachment) => ({
                  url: attachment?.response?.response?.location || attachment?.url,
                  type: attachment.type,
                  name: attachment.name,
                  uid: attachment.uid,
                }))
                : fieldValue;

            // ✅ call reusable dependent evaluator
            this.evaluateDependentFields(cf, cfMap);

            updatedLineCfs = copylineCfs; // store updated lineCustomFields
            break;
          }
        }
        break;
      }
    }
    // ✅ if returnCfData flag is true → just return updated lineCustomFields
    if (returnCfData) {
      return updatedLineCfs;
    }
    const keyUseForUpdateQuantity = grnTypeValue !== 'ADHOC' ? 'received_qty' : 'quantity';
    const modifiedCopyData = copyData?.map((data) => ({
      ...data,
      [keyUseForUpdateQuantity]: data?.lineCustomFields?.find((cf) => cf.fieldName === "Quantity")?.fieldValue || data?.[keyUseForUpdateQuantity],
      offer_price: data?.lineCustomFields?.find((cf) => cf.fieldName === "Rate")?.fieldValue || data?.offer_price,
    }));
    this.setState({ grnTableData: modifiedCopyData }, () => {
      if (isLineWiseDiscount) this.handleDiscountPercentageChange(discountPercentage);
    });
  }

  handleProductChange = (tenantSku, key, productData, isMultiMode, callback) => {
    const {
      isLineWiseDiscount, discountPercentage, grnTableData, selectedSeller, discountType, batchNumbers, visibleLineCfs
    } = this.state;
    const { user, inventoryLocations, cfV2DocGoodReceivingNotes } = this.props;
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const automaticArNumber = user?.tenant_info?.inventory_config?.settings?.enable_auto_generation_of_ar_number;
    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];
      for (let i = 0; i < tenantSku.length; i++) {
        const newData = {
          key: uuidv4(),
          lineCustomFields: visibleLineCfs
        };
        const productSKUData = productData?.find((item) => item?.product_sku_id === tenantSku[i]?.product_sku_id);

        const newDataRow = this.createData({
          tenantSku: tenantSku[i],
          dataItem: newData,
          productData: productSKUData,
          inventoryLocations,
          selectedSeller,
          isLineWiseDiscount,
          discountPercentage,
          autoPrintDescription,
          discountType,
          cfV2DocGoodReceivingNotes
        });
        copyData.push(newDataRow);
      }

      if (callback) callback();

      this.setState((prevState) => {
        return {
          grnTableData: [...prevState?.grnTableData, ...copyData],
          inventoryLocationId: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
          inventoryLocationPath: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
        }
      });
    } else {
      const copyData = JSON.parse(JSON.stringify(grnTableData));
      for (let i = 0; i < copyData?.length; i++) {
        if (copyData[i].key === key) {
          copyData[i] = this.createData({
            tenantSku: tenantSku,
            dataItem: copyData[i],
            productData: productData,
            inventoryLocations,
            selectedSeller,
            isLineWiseDiscount,
            discountPercentage,
            autoPrintDescription,
            discountType,
            cfV2DocGoodReceivingNotes
          });
        }
      }
      this.setState({
        grnTableData: copyData,
        inventoryLocationId: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
        inventoryLocationPath: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
      }, () => {
        if (isLineWiseDiscount) this.handleDiscountPercentageChange(discountPercentage);
      });
    }
  };

  createData = ({
    tenantSku, dataItem, productData, inventoryLocations, selectedSeller, isLineWiseDiscount, discountPercentage, autoPrintDescription, discountType, cfV2DocGoodReceivingNotes
  }) => {
    const { taxesGroup, selectedPoForGrn, user } = this.props;
    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;
    const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes.data.document_line_custom_fields?.filter((item) => item?.is_active), false);

    const isVendorOverseas = (selectedSeller?.seller_info?.seller_type === 'OVERSEAS' || selectedPoForGrn?.seller_info?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;
    const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);
    const newBatch = {
      tenant_product_id: tenantSku?.tenant_product_id,
      expiry_date: tenantSku?.product_info?.expiry_days > 0 ? (dayjs().endOf('day').add(tenantSku?.product_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
      lot_number: '',
      custom_batch_number: `${tenantSku?.product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_info?.product_batch_counter}`,
      cost_price: isApInvoiceEnabled ? 0 : tenantSku?.cost_price || 0,
      mrp: tenantSku?.mrp || 0,
      selling_price: tenantSku?.selling_price || 0,
      uom_id: tenantSku?.uom_id,
      manual_entry: false,
      manufacturingDateFormat: tenantSku?.manufacturing_date_format,
      expiryDateFormat: tenantSku?.expiry_date_format,
      inventory_location_id: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
      inventory_location_path: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
      is_rejected_batch: false,
      seller_id: selectedSeller?.seller_info?.seller_id,
      seller_name: selectedSeller?.seller_info?.seller_name,
      custom_fields: batchCustomFields,
    };
    const copyDataItem = JSON.parse(JSON.stringify(dataItem));
    copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
    copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
    copyDataItem.secondary_uom_qty = 0;
    copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;
    copyDataItem.product_sku_name = tenantSku?.product_info.product_sku_name;
    copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
    copyDataItem.tenant_product_id = tenantSku?.tenant_product_id;
    copyDataItem.product_type = tenantSku?.product_type;
    copyDataItem.product_sku_info = tenantSku?.product_info;
    copyDataItem.manufacturingDateFormat = tenantSku?.manufacturing_date_format;
    copyDataItem.expiryDateFormat = tenantSku?.expiry_date_format;
    copyDataItem.quantity = 1;
    copyDataItem.unitPrice = isApInvoiceEnabled ? 0 : tenantSku?.selling_price || 0;
    copyDataItem.sku = tenantSku?.product_info?.product_sku_id || '';
    copyDataItem.taxInfo = (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info;
    copyDataItem.child_taxes = Helpers.computeTaxation((1 * tenantSku?.cost_price), (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes;
    copyDataItem.taxId = (isVendorOverseas || isApInvoiceEnabled) ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : tenantSku?.product_info?.tax_id;
    copyDataItem.uomId = tenantSku?.purchase_uom_info?.uom_id || 0;
    copyDataItem.uom_info = tenantSku?.purchase_uom_info;
    copyDataItem.uomInfo = tenantSku?.purchase_uom_info;
    copyDataItem.uomGroup = tenantSku?.group_id || tenantSku?.purchase_uom_info?.group_id;
    copyDataItem.productCategoryInfo = tenantSku?.product_category_info || {};
    copyDataItem.uom_list = tenantSku?.uom_list;
    copyDataItem.selectedBatch = newBatch;
    copyDataItem.available_batches = tenantSku?.product_batches
      ? [
        {
          ...newBatch,
        },
        ...tenantSku?.product_batches.map((batch) => ({
          ...batch,
          custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
        })),
      ]
      : [
        {
          ...newBatch,
        },
      ];
    copyDataItem.tally_purchase_account = tenantSku?.tally_stock_group_name;
    copyDataItem.discount = isLineWiseDiscount ? discountPercentage : 0;
    copyDataItem.nextBatchCode = `${tenantSku?.internal_sku_code}/${dayjs().format('DDMMYY')}/${tenantSku?.product_batch_counter}`;
    copyDataItem.expiryDays = tenantSku?.product_info?.expiry_days;
    copyDataItem.received_qty = 1;
    copyDataItem.remarks = autoPrintDescription ? (productData?.description || '')?.replace(/<[^>]+>/g, '') : '';
    copyDataItem.remarkRequired = !!((autoPrintDescription && (productData?.description || '')?.replace(/<[^>]+>/g, '')));
    copyDataItem.lineDiscountType = isLineWiseDiscount ? discountType : 'Percent';
    copyDataItem.offer_price = isApInvoiceEnabled ? 0 : tenantSku?.cost_price || 0;
    copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(lineCFs, [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])])?.map((k) => ({
      ...k,
      fieldValue: k?.fieldName === 'Rate' ? copyDataItem.unitPrice : (k?.fieldName === 'Quantity' ? copyDataItem.quantity : k?.fieldValue),
    }));

    copyDataItem.multiple_batch_info = [{
      key: uuidv4(),
      ...newBatch,
      parentKey: copyDataItem.key,
      sq_no: 1,
      quantity: '',
      ar_number: '',
      expiryDays: tenantSku?.product_info?.expiry_days,
    }];

    return copyDataItem;
  }

  handleMultiProductChange = (tenantSku, key, productData, callback) => {
    this.handleProductChange(tenantSku, key, productData, true, callback);
  }

  handleProductChangeValue = (value, key) => {
    const { grnTableData, discountPercentage } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    for (let i = 0; i < copyData?.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ grnTableData: copyData });
  };

  addNewRow() {
    const { grnTableData, cfGoodReceivingNotesLine, visibleLineCfs } = this.state;
    const { user } = this.props;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    copyData.push({
      key: uuidv4(),
      asset1: '',
      product_sku_name: '',
      quantity: '',
      selling_price: '',
      taxId: '',
      lot: '',
      product_sku_id: null,
      product_sku_info: null,
      child_taxes: [{
        tax_amount: 0,
        tax_type_name: '',
      }],
      lineCustomFields: cfGoodReceivingNotesLine,
      'is_adhoc_line': true,
      // lineCustomFields: visibleLineCfs,
    });
    this.setState({ grnTableData: copyData });
  }

  toggleBatchInner(record, adjustmentRow) {
    const { grnTableData } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    for (let i = 0; i < copyData?.length; i++) {
      const newData = copyData[i];
      if (newData?.key === adjustmentRow?.parentKey) {
        newData.quantity = 0;
        const bundleProducts = newData?.bundle_products;
        for (let j = 0; j < bundleProducts?.length; j++) {
          const rowBatches = bundleProducts[j]?.product_batches;
          for (let k = 0; k < rowBatches?.length; k++) {
            if (rowBatches[k]?.batch_id === record?.batch_id) {
              rowBatches[k].batch_in_use = !rowBatches[k]?.batch_in_use;
            }
            rowBatches[k].consumed_qty = 0;
          }
        }
      }
    }
    this.setState({ grnTableData: copyData });
  }

  toggleBatch(record, adjustmentRow) {
    const { grnTableData } = this.state;
    for (let i = 0; i < grnTableData?.length; i++) {
      if (grnTableData[i]?.key === adjustmentRow?.key) {
        const rowBatches = grnTableData[i]?.product_batches;
        for (let j = 0; j < rowBatches?.length; j++) {
          if (rowBatches[j]?.batch_id === record?.batch_id) {
            rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
            grnTableData[i].quantity -= rowBatches[j].consumed_qty;
            rowBatches[j].consumed_qty = 0;
          }
        }
        break;
      }
    }
    this.setState({ grnTableData });
  }

  addNewChargesRow() {
    const { chargeData } = this.state;
    const copychargeData = JSON.parse(JSON.stringify(chargeData));
    copychargeData.push({
      chargeKey: uuidv4(),
      charge_name: '',
      charge_amount: 0,
      chargesTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
    });
    this.setState({ chargeData: copychargeData });
  }

  addNewBatch(batch, callback) {
    const { grnTableData, selectedLineInfo } = this.state;
    const grnTableDataCopy = JSON.parse(JSON.stringify(grnTableData));

    for (let i = 0; i < grnTableDataCopy.length; i++) {
      if (grnTableDataCopy[i]?.po_line_id === selectedLineInfo?.poLineId && grnTableDataCopy[i]?.po_line_id && selectedLineInfo?.poLineId) {
        grnTableDataCopy[i].selectedBatch = batch;
        grnTableDataCopy[i].available_batches = [
          batch || [],
          ...grnTableDataCopy[i]?.available_batches || [],
        ];
        this.setState({
          availableBatches: grnTableDataCopy[i]?.available_batches,
          selectedLineInfo: {
            ...selectedLineInfo,
            expiryDays: grnTableDataCopy[i]?.expiryDays,
            batchUom: grnTableDataCopy[i]?.product_sku_info?.uom_info,
            selectedBatch: batch,
            uomList: grnTableDataCopy[i]?.product_sku_info?.uom_list,
            tenantProductId: grnTableDataCopy[i]?.product_sku_info?.tenant_product_id,
            poLineId: grnTableDataCopy[i]?.po_line_id,
          },
        });
        break;
      } else if ((grnTableDataCopy[i]?.key === selectedLineInfo?.key && grnTableDataCopy[i]?.key && selectedLineInfo?.key) || (grnTableDataCopy[i]?.grn_line_id === selectedLineInfo?.grn_line_id && grnTableDataCopy[i]?.grn_line_id && selectedLineInfo?.grn_line_id)) {
        grnTableDataCopy[i].selectedBatch = batch;
        grnTableDataCopy[i].available_batches = [
          batch || [],
          ...grnTableDataCopy[i]?.available_batches || [],
        ];
        this.setState({
          availableBatches: grnTableDataCopy[i]?.available_batches,
          selectedLineInfo: {
            ...selectedLineInfo,
            expiryDays: grnTableDataCopy[i]?.expiryDays,
            batchUom: grnTableDataCopy[i]?.uom_info,
            selectedBatch: batch,
            uomList: grnTableDataCopy[i]?.uom_list,
            tenantProductId: grnTableDataCopy[i]?.tenant_product_id,
            key: grnTableDataCopy[i]?.key,
          },
        });
        break;
      }
    }
    callback();
    this.setState({ grnTableData: grnTableDataCopy });
  }

  removeNewBatch() {
    const { grnTableData, selectedLineInfo } = this.state;
    const grnTableDataCopy = JSON.parse(JSON.stringify(grnTableData));
    for (let i = 0; i < grnTableDataCopy.length; i++) {
      if (grnTableDataCopy[i]?.po_line_id === selectedLineInfo?.poLineId && grnTableDataCopy[i]?.po_line_id && selectedLineInfo?.poLineId) {
        grnTableDataCopy[i].selectedBatch = null;
        grnTableDataCopy[i].available_batches = [...grnTableDataCopy[i].available_batches?.filter((item) => item.batch_id)];
        this.setState({
          availableBatches: grnTableDataCopy[i]?.available_batches,
          selectedLineInfo: {
            ...selectedLineInfo,
            expiryDays: grnTableDataCopy[i]?.expiryDays,
            batchUom: grnTableDataCopy[i]?.product_sku_info?.uom_info,
            selectedBatch: null,
            uomList: grnTableDataCopy[i]?.product_sku_info?.uom_list,
            tenantProductId: grnTableDataCopy[i]?.product_sku_info?.tenant_product_id,
            poLineId: grnTableDataCopy[i]?.po_line_id,
          },
        });
        break;
      } else if ((grnTableDataCopy[i]?.key === selectedLineInfo?.key && grnTableDataCopy[i]?.key && selectedLineInfo?.key) || (grnTableDataCopy[i]?.grn_line_id === selectedLineInfo?.grn_line_id && grnTableDataCopy[i]?.grn_line_id && selectedLineInfo?.grn_line_id)) {
        grnTableDataCopy[i].selectedBatch = null;
        grnTableDataCopy[i].available_batches = [...grnTableDataCopy[i].available_batches?.filter((item) => item.batch_id)];
        this.setState({
          availableBatches: grnTableDataCopy[i]?.available_batches,
          selectedLineInfo: {
            ...selectedLineInfo,
            expiryDays: grnTableDataCopy[i]?.expiryDays,
            batchUom: grnTableDataCopy[i]?.uom_info,
            selectedBatch: null,
            uomList: grnTableDataCopy[i]?.uom_list,
            tenantProductId: grnTableDataCopy[i]?.tenant_product_id,
            key: grnTableDataCopy[i]?.key,
          },
        });
        break;
      }
    }
    this.setState({ grnTableData: grnTableDataCopy });
  }

  setSelectedBatch(batch) {
    const { grnTableData, selectedLineInfo } = this.state;
    const grnTableDataCopy = JSON.parse(JSON.stringify(grnTableData));
    for (let i = 0; i < grnTableDataCopy.length; i++) {
      if (grnTableDataCopy[i]?.po_line_id === selectedLineInfo?.poLineId && grnTableDataCopy[i]?.po_line_id && selectedLineInfo?.poLineId) {
        grnTableDataCopy[i].selectedBatch = batch;
        this.setState({
          showLineBatches: false,
          availableBatches: grnTableDataCopy[i]?.available_batches,
          selectedLineInfo: {
            expiryDays: grnTableDataCopy[i]?.expiryDays,
            batchUom: grnTableDataCopy[i]?.product_sku_info?.uom_info,
            selectedBatch: batch,
            uomList: grnTableDataCopy[i]?.product_sku_info?.uom_list,
            tenantProductId: grnTableDataCopy[i]?.product_sku_info?.tenant_product_id,
            poLineId: grnTableDataCopy[i]?.po_line_id,
          },
        });
        break;
      }
      if ((grnTableDataCopy[i]?.key === selectedLineInfo?.key && grnTableDataCopy[i]?.key && selectedLineInfo?.key) || (grnTableDataCopy[i]?.grn_line_id === selectedLineInfo?.grn_line_id && grnTableDataCopy[i]?.grn_line_id && selectedLineInfo?.grn_line_id)) {
        grnTableDataCopy[i].selectedBatch = batch;
        this.setState({
          showLineBatches: false,
          availableBatches: grnTableDataCopy[i]?.available_batches,
          selectedLineInfo: {
            expiryDays: grnTableDataCopy[i]?.expiryDays,
            batchUom: grnTableDataCopy[i]?.uom_info,
            selectedBatch: batch,
            uomList: grnTableDataCopy[i]?.uom_list,
            tenantProductId: grnTableDataCopy[i]?.tenant_product_id,
            key: grnTableDataCopy[i]?.key,
          },
        });
        break;
      }
    }
    this.setState({ grnTableData: grnTableDataCopy });
  }

  getBillFromAddress() {
    const { selectedPo } = this.state;
    const { user, selectedPoForGrn, selectedGRN } = this.props;

    let billFromAddress;
    if (selectedPo) {
      billFromAddress = selectedPo?.billing_address?.state;
    } else if (selectedPoForGrn) {
      billFromAddress = selectedPoForGrn?.billing_address?.state;
    } else if (selectedGRN) {
      selectedGRN?.tenant_billing_address?.state;
    } else {
      billFromAddress = user?.tenant_info?.state;
    }
    return billFromAddress;
  }

  getBillToAddress() {
    const { vendorAddress } = this.state;
    const { selectedPoForGrn } = this.props;

    let billToAddress;
    if (selectedPoForGrn) {
      billToAddress = selectedPoForGrn?.seller_address?.state;
    } else {
      billToAddress = vendorAddress?.state;
    }
    return billToAddress ?? selectedPoForGrn?.seller_address?.state;
  }

  updateLandedCost(grnTableData, chargeData, charge1Value) {
    const totals = this.getLineTotals(grnTableData, chargeData, charge1Value);
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    const updatedData = copyData.map((item, i) => ({
      ...item,
      selectedBatch: {
        ...item?.selectedBatch,
        freight_cost: Number(totals.lines[i].unit_freight_cost) || 0,
        landed_cost: Number(totals.lines[i].unit_landed_cost) || 0,
        other_cost: Number(totals.lines[i].unit_other_cost) || 0,
      },
      multiple_batch_info: item?.multiple_batch_info?.map((row) => ({
        ...row,
        freight_cost: Number(totals.lines[i].unit_freight_cost) || 0,
        landed_cost: Number(totals.lines[i].unit_landed_cost) || 0,
        other_cost: Number(totals.lines[i].unit_other_cost) || 0,
      })),
    }));
    this.setState({ grnTableData: updatedData }, () => {
      if (this.state?.isLineWiseDiscount) this.handleDiscountPercentageChange(this.state.discountPercentage);
    });
  }

  customFieldVisibilityChange(visible, cfId) {
    const { visibleLineCfs } = this.state;
    let visibleLineCfsCopy = JSON.parse(JSON.stringify(visibleLineCfs));
    visibleLineCfsCopy = visibleLineCfsCopy?.map((customField) => {
      if (customField?.cfId === cfId) {
        return { ...customField, visible };
      }
      return { ...customField };
    });
    this.setState({ visibleLineCfs: visibleLineCfsCopy });
  }

  onBulkUploadBatch(batchData, lineId) {
    const { grnTableData, grnTypeValue } = this.state;
    const mapCustomFieldsForObject = (batchCustomFieldsData, importedItemLines) => {
      const mappedCustomFields = batchCustomFieldsData?.map((batchField) => {
        // Find a matching field in the importedData's customFields array
        const matchingField = importedItemLines?.customFields?.find(
          (cf) => cf.id == batchField?.cfEntityId,
        );
        let fieldValue = matchingField?.value || '';
        // If field type is DATE and the value is not a valid date or is a number, set it to null
        if (batchField.fieldType === 'DATE' && (isNaN(Date.parse(fieldValue)) || typeof fieldValue === 'number')) {
          fieldValue = null;
        }
        return {
          ...batchField,
          fieldValue,
        };
      });
      // Return the imported item with updated customFields
      return mappedCustomFields;
    };

    const copyData = JSON.parse(JSON.stringify(grnTableData));
    for (let i = 0; i < copyData?.length; i++) {
      if (copyData[i].key === lineId) {
        // Map and update batch data with custom fields
        const updatedBatchData = batchData?.map((lines) => ({
          ...lines,
          custom_fields: mapCustomFieldsForObject(copyData[i]?.multiple_batch_info?.[0]?.custom_fields, lines) || [],
        }));
        // Assign the updated batch data to multiple_batch_info
        copyData[i].multiple_batch_info = updatedBatchData || [];

        // Calculate total quantity from batchData
        const keyName = grnTypeValue === 'ADHOC' ? 'quantity' : 'received_qty';
        copyData[i][keyName] = batchData?.reduce((acc, item) => acc + Number(item.quantity), 0);

      }
    }
    this.setState({ grnTableData: copyData });
  }

  restrictMessage() {
    const {
      priceMasking,
      user
    } = this.props;
    const { grnTypeValue, isAdhocGrnAllowed } = this.state;

    const messages = []; // Collect restriction messages her

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    if (isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice)) {
      messages.push(
        `You don't have access to view/edit${isHideCostPrice ? ' cost price' : ''}${isHideCostPrice && isHideSellingPrice ? ' and' : ''}${isHideSellingPrice ? ' selling price' : ''}.`
      );
    } else if (grnTypeValue === 'ADHOC' && isAdhocGrnAllowed) {
      messages.push(
        'You don\'t have access to create ADHOC GRN in the selected business unit.'
      );
    }

    return messages; // Return collected messages or an empty array
  }

  getGRNTableData = (data) => {
    const copyData = JSON.parse(JSON.stringify(data));
    return [...copyData?.filter((item) => item?.product_sku_id), ...copyData?.filter((item) => !item?.product_sku_id)]
  }

  handleDiscountPercentageChange = (value) => {
    const { grnTableData, discountType, grnTypeValue } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    grnTypeValue !== 'ADHOC' ?
      copyData.map((item) => {
        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.received_qty * cur.offer_price), 0);

        const discountValue = discountType === 'Percent' ? parseFloat(value) : ((item.received_qty * item.offer_price) / parseFloat(totalValue)) * parseFloat(value);

        const taxableValue = discountType === 'Percent'
          ? (item?.received_qty * item?.offer_price) * (1 - discountValue / 100)
          : Math.max(item.received_qty * item?.offer_price - discountValue, 0);

        item.discount = discountValue;
        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes;
        return item;
      }) :
      copyData.map((item) => {
        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

        const discountValue = discountType === 'Percent' ? parseFloat(value) : ((item.quantity * item.offer_price) / parseFloat(totalValue)) * parseFloat(value);

        const taxableValue = discountType === 'Percent'
          ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
          : Math.max(item.quantity * item?.offer_price - discountValue, 0);

        item.discount = discountValue;
        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes;
        return item;
      })
    this.setState({ grnTableData: copyData, discountPercentage: parseFloat(value) });
  }

  updateBatchCfs(fieldValue, cfId, record) {
    const { grnTableData } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    for (const item of copyData) {
      if (((record.key === item?.key && item?.key) || (record.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (record.po_line_id === item?.po_line_id && item?.po_line_id))) {
        const copyLineCfs = item?.selectedBatch?.custom_fields;
        for (const cf of copyLineCfs) {
          if (cf.cfId === cfId) {
            cf.fieldValue = cf.fieldType === 'ATTACHMENT' ? fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) : fieldValue;
            break;
          }
        }
        break;
      }
    }
    this.setState({ grnTableData: copyData });
  }
  updateBatchCfsForMultiBatchMode(fieldValue, cfId, record) {
    const { grnTableData } = this.state;
    const copyData = JSON.parse(JSON.stringify(grnTableData));

    for (const item of copyData) {
      if (
        (record?.parentKey === item?.key && item?.key) ||
        (record?.grn_line_id === item?.grn_line_id && item?.grn_line_id) ||
        (record?.po_line_id === item?.po_line_id && item?.po_line_id)
      ) {
        if (item?.multiple_batch_info) {
          item.multiple_batch_info = item?.multiple_batch_info?.map(batchItem => {
            if (batchItem?.key === record?.key && batchItem?.custom_fields) {
              batchItem.custom_fields = batchItem?.custom_fields.map(cf => {
                if (cf.cfId === cfId) {
                  return {
                    ...cf,
                    fieldValue: cf.fieldType === 'ATTACHMENT'
                      ? fieldValue.map(attachment => ({
                        url: attachment?.response?.response?.location || attachment?.url,
                        type: attachment.type,
                        name: attachment.name,
                        uid: attachment.uid,
                      }))
                      : fieldValue,
                  };
                }
                return cf;
              });
            }
            return batchItem;
          });
        }
        break;
      }
    }
    this.setState({ grnTableData: copyData });
  }

  render() {
    const {
      createGRNLoading, user, getPurchaseOrders, purchaseOrders, match, selectedGRN, selectedPurchaseOrder, CurrenciesResults, getCurrenciesLoading, workOrder, getGRNByIdLoading, getTenantsConfiguration, purchaseOrdersV2, getPurchaseOrdersV2, getPurchaseOrdersV2Loading,
      getPurchaseOrderByIdSuccess, getPurchaseOrderById, updateGRNLoading, selectedPoForGrn, inventoryLocations, MONEY, getInventoryLocations, uploadDocumentOCR, getDocCFV2GoodReceivingNotesLoading, cfV2DocGoodReceivingNotes, priceMasking, getSellers, getInventoryLocationsLoading, taxesGroup,
    } = this.props;
    const {
      dueDate, grnTableData, selectedSeller, grnDate, invoiceDate, terms, isLineWiseDiscount, discountPercentage, selectedCurrencyID, selectedCurrencyName, billingAddress, checkedRecipients, toRecipients,
      buttonClick, openPurchaseOrderModal, selectedTenantSeller, purchaseAccount, grnTypeValue, allStock, tenantDepartmentId, multipleBatchValidation, showAddressDrawer, selectedAddressType, isAdhocGrnAllowed, openDrawerOfMultipleSelectedPos,
      vendorAddress, shippingAddress, fileList, invoiceNumber, showLineBatches, selectedLineInfo, selectedPo, selectedPoValue, cfGoodReceivingNotesDoc, formSubmitted, availableBatches, ewayBillNumber, ewayBillList, toggleRejectedBatches, chargeData, showModal, getHistory, latestGrnId, taxType, taxTypeId, taxTypeName, charge1Name, charge1Value, isBatchValid, selectedSellerId, discountType, selectedTags, freightTax, freightTaxId, freightTaxInfo, openFreightTax, freightSacCode, cfGoodReceivingNotesLine, visibleLineCfs, inventoryLocationId, inventoryLocationPath, isAutomaticConversionRate, currencyConversionRate, gstNumber, selectedTenant, updateDocumentReason, freightTaxData, selectedTenantTallyIntegrationId, narration, grnNumber, docSeqId, paymentTerms, paymentRemarks,
    } = this.state;
    const isReceivedQty = grnTableData?.every((item) => {
      const itemQuantityToUse = grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty;
      return itemQuantityToUse > 0;
    });
    const allowAdhoc = user?.tenant_info?.config__grn__allow_adhoc;

    const addNewAdhocLineInPoToGrnAllowed = selectedPoForGrn?.subcontractor_mo_id ? false : selectedPo?.subcontractor_mo_id ? false : selectedPo?.subcontractor_mo_id ? false : true;
    const accountingGSTTransactionAsPerMaster = user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';
    let copyDocLevelError;
    let copyLineLevelError;
    if (formSubmitted) {
      const { docLevelError, lineLevelError } = this.getGrnErrors();
      copyDocLevelError = docLevelError;
      copyLineLevelError = lineLevelError;
    }

    const { isDataMaskingPolicyEnable, isHideCostPrice } = priceMasking;

    const purchaseAccountList = user?.user_tenants?.find((item) => item?.tenant_id === selectedTenant)?.tally_configuration?.purchase_account_list;


    const splitChargesData = (charge) => {
      const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
      const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
      return { chargeWithTaxName, chargeWithoutTaxName };
    };
    const renderCharges = (charge) => charge?.map((line) => (
      <div
        key={line?.chargeKey}
        className="form-calculator__field"
      >
        <div className="form-calculator__field-name">
          {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.use_custom_charges ? (
            <div className="select_extra_charge_wrapper">
              <SelectExtraCharge
                containerClassName="orgInputContainer"
                selectedChargeName={line.charge_name}
                disabled={createGRNLoading || updateGRNLoading}
                onChange={(value) => {
                  const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                  copyChargeData.map((item) => {
                    if (item.chargeKey === line.chargeKey) {
                      // eslint-disable-next-line no-param-reassign
                      item.charge_name = value?.ledger_name;
                      item.chargesSacCode = (value?.charge_sac_code) || null;
                    }
                  });
                  this.setState({ chargeData: copyChargeData });
                  this.updateLandedCost(grnTableData, copyChargeData, charge1Value);
                }}
                customStyle={{
                  width: '220px',
                  backgroundColor: 'white',
                }}
                excludeCharges={chargeData?.map((item) => item?.charge_name)}
                entityName="PURCHASE"
              />
              {line?.chargesTax && (selectedSeller ? selectedSeller?.seller_info?.seller_type !== 'OVERSEAS' : selectedPoForGrn?.seller_info?.seller_type !== 'OVERSEAS') && (
                <div style={{
                  color: '#2d7df7',
                  fontWeight: '400',
                  fontSize: '12px',
                }}
                >
                  {`tax@${line?.chargesTax}%`}
                </div>
              )}
            </div>
          ) : (
            <H3FormInput
              value={line?.charge_name}
              type="text"
              containerClassName={`orgInputContainer ${formSubmitted
                && Number(line?.charge_name) <= 0
                ? 'form-error__input'
                : ''
                }`}
              placeholder="Charge name.."
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    // eslint-disable-next-line no-param-reassign
                    item.charge_name = e.target.value;
                  }
                });
                this.setState({ chargeData: copyChargeData });
                this.updateLandedCost(grnTableData, copyChargeData, charge1Value);
              }}
              disabled={createGRNLoading || updateGRNLoading}
            />
          )}

        </div>
        <div className="form-calculator__field-value" style={{ display: 'flex', gap: '0px' }}>
          <div style={{ width: '140px', marginRight: '-27px' }}>
            <H3FormInput
              value={line?.charge_amount}
              type="number"
              containerClassName={`orgInputContainer ${formSubmitted
                && Number(line?.charge_amount) <= 0
                ? 'form-error__input'
                : ''
                }`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    const updatedChargeAmount = parseFloat(e.target.value) || 0;

                    // Update charge_amount
                    item.charge_amount = updatedChargeAmount;
                    // Compute chargesTaxData with updated values
                    const computedTaxData = Helpers.computeTaxation(
                      updatedChargeAmount,
                      line.chargesTaxInfo,
                      this.getBillFromAddress(),
                      this.getBillToAddress(),
                    );
                    // Update chargesTaxData
                    item.chargesTaxData = {
                      ...line?.chargesTaxInfo,
                      child_taxes: computedTaxData?.tax_info?.child_taxes || [],
                    };
                  }
                  return grnTableData;
                });
                this.setState({ chargeData: copyChargeData });
                this.updateLandedCost(grnTableData, copyChargeData, charge1Value);
              }}
              allowNegative
              disabled={createGRNLoading || updateGRNLoading}
            />
          </div>
          <div className={isVendorOverseas ? 'display-none' : ''}>
            <ChargesTaxInput
              selectedTenant={selectedTenant}
              selectedChargeName={line.charge_name}
              openChargesTax={line?.openChargesTax}
              setOpenChargesTax={(value) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    // eslint-disable-next-line no-param-reassign
                    item.openChargesTax = value;
                  }
                  return grnTableData;
                });
                this.setState({ chargeData: copyChargeData });
              }}
              sacCode={line?.chargesSacCode}
              setSacCode={(value) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    // eslint-disable-next-line no-param-reassign
                    item.chargesSacCode = value;
                  }
                  return grnTableData;
                });
                this.setState({ chargeData: copyChargeData });
              }}
              chargesTaxId={line?.chargesTaxId}
              setChargesTaxData={(value) => {
                const updatedChargeData = chargeData?.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    return {
                      ...item,
                      chargesTaxId: !value ? 'Not Applicable' : value?.tax_id,
                      chargesTax: !value ? null : value?.tax_value,
                      chargesTaxInfo: !value ? null : value,
                      chargesTaxData: !value ? {
                        child_taxes: [
                          {
                            tax_amount: 0,
                            tax_type_name: '',
                          },
                        ],
                      } : {
                        ...value,
                        child_taxes: Helpers.computeTaxation(line?.charge_amount, value, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes,
                      },
                    };
                  }
                  return item;
                });
                this.setState({ chargeData: updatedChargeData });
              }}
              tallyLedgerName={line?.tallyLedgerName}
              setTallyLedgerName={(value) => {
                this.setState((prevState) => {
                  const updatedChargeData = prevState?.chargeData?.map((item) => {
                    if (item?.chargeKey === line?.chargeKey) {
                      return {
                        ...item,
                        tallyLedgerName: value,
                      };
                    }
                    return item;
                  });
                  return { chargeData: updatedChargeData };
                });
              }}
              showTallyLedgerPurchase
            />
          </div>
          <div
            className="form-calculator__delete-line-button"
            onClick={() => this.handleDeleteCharge(line?.chargeKey)}
          >
            <FontAwesomeIcon
              icon={faCircleXmark}
              size="sm"
              style={{ color: '#6f7276' }}
            />
          </div>
        </div>
      </div>
    ))
    const handleMultiPoChange = (dataArray) => {
      const tempSelectedPoList = dataArray?.filter(data => selectedPoValue?.every(item => item?.value !== data.value))?.map(data => data.value);
      const selectedPoList = dataArray?.map(data => data.value);
      const oldGrnLine = grnTableData?.filter(
        item => selectedPoList?.includes(item?.po_id) || item?.is_adhoc_line
      );
      const selectedPurchaseOrders = purchaseOrdersV2?.purchase_order?.filter(po => tempSelectedPoList?.includes(po.po_id));
      const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
      const batchCustomFields = CustomFieldHelpers.getCfStructure(
        cfV2DocGoodReceivingNotes?.data?.batch_custom_fields,
        true
      );
      let combinedLines = [];
      let combinedCharges = [];
      let combinedAttachments = [];
      let defaultDueDate = dayjs();

      let updateCaseGstNumber = selectedGRN?.gst_number;
      let updateCaseVendorAddress = selectedGRN?.seller_address_info;
      let updateCaseCurrencyInfo = selectedGRN?.currency_info;
      let updateCaseConversionRate = selectedGRN?.conversion_rate;

      let gstNumber = updateCaseGstNumber || selectedSeller?.seller_info?.gst_number;
      let vendorAddress = updateCaseVendorAddress || selectedSeller?.seller_info?.office_address_details;
      let currencyInfo = updateCaseCurrencyInfo || selectedSeller?.currency_info;
      let conversionRate = updateCaseConversionRate || currencyInfo.conversion_rate;

      const isVendorOverseas = selectedSeller?.seller_info?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

      selectedPurchaseOrders.forEach(po => {
        combinedAttachments.push(...(po.attachments || []));
        const _dueDate = dayjs().add(Number(po?.payment_terms?.[0]?.due_days || 0), 'days');
        if (_dueDate.isAfter(defaultDueDate)) defaultDueDate = _dueDate;
        po.purchase_order_lines?.forEach(item => {
          const newBatch = {
            tenant_product_id: item?.tenant_product_id,
            expiry_date:
              item?.product_sku_info?.expiry_days > 0
                ? dayjs().endOf('day').add(item?.product_sku_info?.expiry_days, 'day')
                : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
            lot_number: '',
            custom_batch_number: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
            cost_price:
              item?.offer_price && po?.conversion_rate
                ? (item?.offer_price * po?.conversion_rate)?.toFixed(DEFAULT_CUR_ROUND_OFF)
                : item?.offer_price || 0,
            mrp: item?.product_sku_info?.selling_price || 0,
            selling_price: 0,
            manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
            expiryDateFormat: item?.product_sku_info?.expiry_date_format,
            tenant_department_id: po?.tenant_department_id,
            uom_id: item?.uom_id,
            manual_entry: false,
            inventory_location_id: null,
            inventory_location_path: null,
            is_rejected_batch: false,
            custom_fields: batchCustomFields,
          };

          const parentKey = uuidv4();
          const discountValue = item?.is_discount_in_percent
            ? Number(item?.line_discount_percentage)
            : Number(item?.line_discount_amount);

          const taxableValue =
            discountType === 'Percent'
              ? item?.received_qty * item?.offer_price * (1 - discountValue / 100)
              : Math.max(item.received_qty * item?.offer_price - discountValue, 0);
          combinedLines.push({
            ...item,
            source_po_id: po.po_id,
            sourcePONumber: po.po_number,
            key: parentKey,
            grn_entity_line_id: item?.po_line_id,
            received_qty: item?.received_qty,
            total_pending_qty: 0,
            secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
            secondaryUomId: item?.product_sku_info?.secondary_uom_id,
            secondary_uom_qty: 0,
            expiryDate:
              item?.product_sku_info?.expiry_days > 0
                ? dayjs().add(item?.product_sku_info?.expiry_days || 0, 'day').format('YYYY-MM-DD')
                : INFINITE_EXPIRY_DATE,
            taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_info,
            child_taxes: Helpers.computeTaxation(
              taxableValue,
              isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_info,
              po?.billing_address?.state,
              po?.seller_address?.state
            )?.tax_info?.child_taxes,
            taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
            uomId: item?.uom_id,
            uomInfo: item?.uom_info?.[0],
            remarks: autoPrintDescription ? (item?.remark || '')?.replace(/<[^>]+>/g, '') : '',
            remarkRequired: !!(autoPrintDescription && (item?.remark || '')?.replace(/<[^>]+>/g, '')),
            selectedBatch: newBatch,
            available_batches: item?.available_batches
              ? [
                newBatch,
                ...item?.available_batches?.map(batch => ({
                  ...batch,
                  custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
                })),
              ]
              : [newBatch],
            expiryDays: item?.product_sku_info?.expiry_days,
            tally_purchase_account: item?.product_sku_info?.tally_stock_group_name,
            nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
            multiple_batch_info: [
              {
                key: uuidv4(),
                ...newBatch,
                parentKey,
                sq_no: 1,
                quantity: '',
                ar_number: '',
                expiryDays: item?.product_sku_info?.expiry_days,
              },
            ],
            discount: discountValue,
            lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
            lineCustomFields: FormHelpers.lineSystemFieldValue(
              cfGoodReceivingNotesLine,
              item?.po_line_custom_fields
            ),
          });
        });
      });
      // Fetch inventory locations and update state
      getInventoryLocations(tenantDepartmentId, '', null, true, null, null, locations => {
        const location = locations?.inventory_location?.filter(i => i?.is_active)?.[0];
        const combineBothLines = [...combinedLines, ...oldGrnLine];
        this.setState({
          selectedPoList,
          selectedPoValue: dataArray,
          gstNumber,
          fileList: combinedAttachments,
          vendorAddress,
          shippingAddress,
          dueDate: defaultDueDate,
          selectedCurrencyID: currencyInfo?.org_currency_id,
          selectedCurrencyName: currencyInfo,
          currencyConversionRate: conversionRate,
          isAutomaticConversionRate: false,
          grnTableData: FormHelpers.updatedBatchNumber(
            combineBothLines.map(line => ({
              ...line,
              inventoryLocationId: location?.inventory_location_id,
              inventoryLocationPath: location?.inventory_location_path,
              selectedBatch: {
                ...line.selectedBatch,
                inventory_location_id: location?.inventory_location_id,
                inventory_location_path: location?.inventory_location_path,
              },
            }))
          ),
          inventoryLocationId: location?.inventory_location_id,
          inventoryLocationPath: location?.inventory_location_path,
          chargeData: combinedCharges.map(charge => ({
            ...charge,
            chargeKey: uuidv4(),
            chargesTaxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : charge?.tax_info?.tax_id,
            chargesSacCode: isVendorOverseas ? '' : charge?.charge_sac_code,
            chargesTaxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : charge?.tax_info,
            chargesTax: isVendorOverseas ? 0 : charge?.tax_info?.tax_value,
            tallyLedgerName: charge?.ledger_name || null,
            chargesTaxData: {
              ...charge?.tax_info,
              child_taxes: Helpers.computeTaxation(
                charge?.charge_amount,
                charge?.tax_info,
                vendorAddress?.state,
                shippingAddress?.state
              )?.tax_info?.child_taxes,
            },
          })),
        });
      });
    };
    const handlePoChange = (data) => {
      const selectedPurchaseOrderCopy = purchaseOrdersV2?.purchase_order?.filter(
        (item) => item.po_id === Number(data?.value),
      );
      const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
      const _dueDate = dayjs().add(Number(selectedPurchaseOrderCopy?.payment_terms?.[0]?.due_days || 0), 'days');
      const automaticArNumber = user?.tenant_info?.inventory_config?.settings?.enable_auto_generation_of_ar_number;
      const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);

      const isVendorOverseas = selectedSeller?.seller_info?.seller_type === 'OVERSEAS' && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

      getInventoryLocations(selectedPurchaseOrderCopy?.[0]?.tenant_department_id, '', null, true, null, null, (locations) => {
        this.setState({
          selectedPo: selectedPurchaseOrderCopy?.[0],
          gstNumber: selectedPurchaseOrderCopy?.[0]?.tenant_seller_info?.gst_number,
          tenantDepartmentId: selectedPurchaseOrderCopy?.[0]?.tenant_department_id,
          fileList: selectedPurchaseOrderCopy?.[0]?.attachments,
          vendorAddress: selectedPurchaseOrderCopy?.[0]?.seller_address,
          shippingAddress: selectedPurchaseOrderCopy?.[0]?.shipping_address,
          selectedPoValue: data.isArray ? data : [data],
          dueDate: _dueDate,
          taxType: isVendorOverseas ? null : selectedPurchaseOrderCopy?.[0]?.tcs_id || null,
          taxTypeId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : (selectedPurchaseOrderCopy?.[0]?.tcs_info ? selectedPurchaseOrderCopy?.[0]?.tcs_info?.tax_id : null),
          taxTypeInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : (selectedPurchaseOrderCopy?.[0]?.tcs_info || null),
          taxTypeName: selectedPurchaseOrderCopy?.[0]?.tcs_info ? selectedPurchaseOrderCopy?.[0]?.tcs_info?.tax_type_name : 'TDS',
          chargeData:
            selectedPurchaseOrderCopy?.[0]?.other_charges?.map((otherCharge) => ({
              ...otherCharge,
              chargeKey: uuidv4(),
              chargesTaxId: otherCharge?.tax_info?.tax_id,
              chargesSacCode: otherCharge?.charge_sac_code,
              chargesTaxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info,
              chargesTax: otherCharge?.tax_info?.tax_value,
              tallyLedgerName: otherCharge?.ledger_name || null,
              chargesTaxData: {
                ...otherCharge?.tax_info,
                child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, otherCharge?.tax_info, selectedPurchaseOrderCopy?.[0]?.billing_address?.state, selectedPurchaseOrderCopy?.[0]?.seller_address?.state)?.tax_info?.child_taxes,
              },
            })) || [],
          discountType: selectedPurchaseOrderCopy?.[0]?.is_discount_in_percent ? 'Percent' : 'Amount',
        });
        let grnData = selectedPurchaseOrderCopy?.[0]?.purchase_order_lines?.map((item) => {
          const newBatch = {
            tenant_product_id: item?.tenant_product_id,
            expiry_date: item?.product_sku_info?.expiry_days > 0 ? (dayjs().endOf('day').add(item?.product_sku_info?.expiry_days, 'day')) : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
            lot_number: '',
            custom_batch_number: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
            cost_price: isApInvoiceEnabled ? 0 : (item?.offer_price && selectedPurchaseOrderCopy?.[0]?.conversion_rate) ? (item?.offer_price * selectedPurchaseOrderCopy?.[0]?.conversion_rate)?.toFixed(DEFAULT_CUR_ROUND_OFF) : (item?.offer_price || 0),
            mrp: item?.product_sku_info?.selling_price || 0,
            selling_price: 0,
            manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
            expiryDateFormat: item?.product_sku_info?.expiry_date_format,
            tenant_department_id: selectedPurchaseOrderCopy?.[0]?.tenant_department_id,
            uom_id: item?.uom_id,
            manual_entry: false,
            inventory_location_id: locations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
            inventory_location_path: locations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
            is_rejected_batch: false,
            custom_fields: batchCustomFields,
          };
          const parentKey = uuidv4();
          const discountValue = item?.is_discount_in_percent ? Number(item?.line_discount_percentage) : Number(item?.line_discount_amount);

          const taxableValue = discountType === 'Percent'
            ? (item?.received_qty * item?.offer_price) * (1 - discountValue / 100)
            : Math.max(item.received_qty * item?.offer_price - discountValue, 0);

          return ({
            ...item,
            key: parentKey,
            received_qty: 0,
            total_pending_qty: 0,
            secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
            secondaryUomId: item?.product_sku_info?.secondary_uom_id,
            secondary_uom_qty: 0,
            expiryDate: item?.product_sku_info?.expiry_days > 0 ? (dayjs().add(item?.product_sku_info?.expiry_days || 0, 'day')).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE,
            taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_info,
            child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_info, selectedPurchaseOrderCopy?.[0]?.billing_address?.state, selectedPurchaseOrderCopy?.[0]?.seller_address?.state)?.tax_info?.child_taxes,
            taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
            uomId: item?.uom_id,
            uomInfo: item?.uom_info?.[0],
            remarks: autoPrintDescription ? (item?.remark || '')?.replace(/<[^>]+>/g, '') : '',
            remarkRequired: !!((autoPrintDescription && (item?.remark || '')?.replace(/<[^>]+>/g, ''))),
            selectedBatch: newBatch,
            manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
            expiryDateFormat: item?.product_sku_info?.expiry_date_format,
            available_batches: item?.available_batches
              ? [
                {
                  ...newBatch,
                },
                ...item?.available_batches?.map((batch) => ({
                  ...batch,
                  custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
                })),
              ]
              : [
                {
                  ...newBatch,
                },
              ],
            expiryDays: item?.product_sku_info?.expiry_days,
            tally_purchase_account: item?.product_sku_info?.tally_stock_group_name,
            nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
            multiple_batch_info: [{
              key: uuidv4(),
              ...newBatch,
              parentKey,
              sq_no: 1,
              quantity: '',
              ar_number: '',
              expiryDays: item?.product_sku_info?.expiry_days,
            }],
            discount: discountValue,
            lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
            lineCustomFields: FormHelpers.lineSystemFieldValue(cfGoodReceivingNotesLine, item?.po_line_custom_fields),
          });
        });

        const uniqueVisibleLineCfs = new Set([
          ...(
            cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || []
          ),
          ...(
            selectedPurchaseOrderCopy?.[0]?.purchase_order_lines?.reduce((acc, line) => acc.concat(line?.po_line_custom_fields || []), []) || []
          ),
        ]);

        const docCf = CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
        const grnCf = selectedPurchaseOrderCopy?.[0]?.custom_fields?.filter((item) => (item?.is_active)) || [];
        const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, grnCf) || [];

        this.setState({
          selectedCurrencyID: selectedPurchaseOrderCopy?.[0]?.org_currency_info?.org_currency_id,
          selectedCurrencyName: selectedPurchaseOrderCopy?.[0]?.org_currency_info,
          grnTableData: FormHelpers.updatedBatchNumber(grnData),
          isLineWiseDiscount: !selectedPurchaseOrderCopy?.[0]?.is_line_wise_discount,
          inventoryLocationId: locations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
          inventoryLocationPath: locations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
          currencyConversionRate: selectedPurchaseOrderCopy?.[0]?.conversion_rate,
          isAutomaticConversionRate: false,
          charge1Name: selectedPurchaseOrderCopy?.[0]?.charge_1_name || 'Freight',
          charge1Value: selectedPurchaseOrderCopy?.[0]?.charge_1_value,
          freightTaxId: selectedPurchaseOrderCopy?.[0]?.freight_tax_id || 'Not Applicable',
          freightTax: selectedPurchaseOrderCopy?.[0]?.freight_tax_info?.tax_value,
          freightTaxInfo: selectedPurchaseOrderCopy?.[0]?.freight_tax_info,
          freightTaxData: {
            ...selectedPurchaseOrderCopy?.[0]?.freight_tax_info,
            child_taxes: Helpers.computeTaxation(selectedPurchaseOrderCopy?.[0]?.charge_1_value, selectedPurchaseOrderCopy?.[0]?.freight_tax_info, selectedPurchaseOrderCopy?.[0]?.billing_address?.state, selectedPurchaseOrderCopy?.[0]?.seller_address?.state)?.tax_info?.child_taxes
          },
          freightSacCode: selectedPurchaseOrderCopy?.[0]?.freight_sac_code,
          discountPercentage: selectedPurchaseOrderCopy?.[0]?.is_discount_in_percent ? selectedPurchaseOrderCopy?.[0]?.discount_percentage : selectedPurchaseOrderCopy?.[0]?.discount_amount,
          discountType: selectedPurchaseOrderCopy?.[0]?.is_discount_in_percent ? 'Percent' : 'Amount',
          cfGoodReceivingNotesDoc: mergedCf,
          paymentTerms: selectedPurchaseOrderCopy?.[0]?.payment_terms?.[0]?.due_days || 0,
          paymentRemarks: selectedPurchaseOrderCopy?.[0]?.payment_terms?.[0]?.remark || '',
        });
      });
    };
    const multiPoGRN = selectedGRN?.grn_entity_id === null && selectedGRN?.grn_entity_type === 'PURCHASE_ORDER';

    const isVendorOverseas = (selectedSeller?.seller_info?.seller_type === 'OVERSEAS' || selectedPoForGrn?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

    const isApInvoiceEnabled = user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;


    return (
      <Fragment>
        <div>
          {getGRNByIdLoading ? (
            <FormLoadingSkull />
          ) : (
            <Fragment>
              <div
                className="form__wrapper"
                style={{ paddingTop: (selectedPoForGrn?.po_id) ? '0px' : '90px' }}
              >
                {(formSubmitted && (copyDocLevelError?.length > 0 || copyLineLevelError?.length > 0)) && (
                  <ErrorHandle message='Mandatory fields required' docLevelErrors={copyDocLevelError} lineLevelErrors={copyLineLevelError} />
                )}
                <div className="ant-row">
                  <div className="ant-col-md-24">
                    <div className="form__section">
                      <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                        <H3Text text="PART A" className="form__section-title" />
                        <div className="form__section-line" />
                        <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center', gap: '2px' }}>
                          <CurrencyConversionV2
                            selectedCurrencyName={selectedCurrencyName}
                            selectedCurrencyID={selectedCurrencyID}
                            isAutomaticConversionRate={isAutomaticConversionRate}
                            currencyConversionRate={currencyConversionRate}
                            setCurrencyConversionRate={(val) => this.setState({ currencyConversionRate: val })}
                            setIsAutomaticConversionRate={(val) => this.setState({ isAutomaticConversionRate: val })}
                            setSelectedCurrencyName={(val) => this.setState({ selectedCurrencyName: val })}
                            setSelectedCurrencyID={(val) => this.setState({ selectedCurrencyID: val })}
                          />
                          <CustomDocumentInputs
                            customFields={cfGoodReceivingNotesDoc}
                            updateCustomFields={(cf) => this.setState({ cfGoodReceivingNotesDoc: cf })}
                          />
                        </div>
                      </div>
                      <div className="form__section-inputs mg-bottom-20">
                        <div className="ant-row">
                          <div className="ant-col-md-24">
                            {!selectedPoForGrn && !selectedGRN && (
                              <div className="form__input-row" style={{ alignItems: 'center', marginBottom: '15px' }}>
                                <Radio.Group
                                  disabled={createGRNLoading || updateGRNLoading || getDocCFV2GoodReceivingNotesLoading || match?.params?.grnId || selectedPoForGrn?.po_id}
                                  onChange={(event) => {
                                    this.setState({
                                      grnTypeValue: event.target.value,
                                      selectedSeller: null,
                                      selectedPoValue: [],
                                      vendorAddress: null,
                                      selectedPo: null,
                                      selectedPoJson: null,
                                      grnTableData: (event.target.value === 'ADHOC') ? [{
                                        key: uuidv4(),
                                        child_taxes: [{
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        }],
                                        lineCustomFields: cfGoodReceivingNotesLine,
                                      }] : [],
                                      shippingAddress: null,
                                      selectedTenantSeller: null,
                                      formSubmitted: null,
                                      invoiceNumber: '',
                                      selectedCurrencyID: null,
                                      selectedCurrencyName: null,
                                      isAutomaticConversionRate: null,
                                      currencyConversionRate: null,
                                      tenantDepartmentId: event.target.value === 'ADHOC' || event.target.value === 'MULTIPO' ? user?.tenant_info?.default_store_id : null,
                                      visibleLineCfs: cfGoodReceivingNotesLine,
                                      selectedTenant: user?.tenant_info?.tenant_id,
                                      selectedTenantTallyIntegrationId: user?.tenant_info?.it_id,
                                      narration: '',
                                      discountPercentage: null,
                                      cfGoodReceivingNotesDoc: CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.document_custom_fields, true) || [],
                                      paymentTerms: 0,
                                      paymentRemarks: '',
                                    });
                                  }}
                                  value={grnTypeValue}
                                  className="mg-top-5"
                                >
                                  <Radio value="Purchase Order">Purchase Order</Radio>
                                  <Radio value="ADHOC" disabled={!allowAdhoc}>ADHOC</Radio>
                                  <Radio value="MULTIPO">MULTI PO</Radio>
                                </Radio.Group>
                              </div>
                            )}
                          </div>
                          <DocumentNumberSeqInput
                            valueFromProps={grnNumber}
                            updateCase={match?.params?.grnId}
                            setInitialDocSeqNumber={(value) => this.setState({ initialGrnNumber: value })}
                            entityName="GOOD_RECEIVING_NOTE"
                            docSeqId={docSeqId}
                            tenantId={selectedTenant}
                            onChangeFromProps={(event, newValue, seqId) => {
                              this.setState({
                                grnNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                                docSeqId: seqId,
                              })
                            }}
                            docTitle="GRN#"
                            formSubmitted={formSubmitted}
                          />
                          {grnTypeValue === 'ADHOC' &&
                            <div className="ant-col-md-6">
                              <TenantSelector
                                selectedTenant={selectedTenant}
                                showSearch
                                onChange={(value) => {
                                  getTenantsConfiguration(value, (tenantData) => {
                                    this.setState({
                                      isAdhocGrnAllowed: !tenantData?.config__grn__allow_adhoc
                                    })
                                  });
                                  const copychargeData = [];
                                  copychargeData.push({
                                    chargeKey: uuidv4(),
                                    charge_name: '',
                                    charge_amount: 0,
                                    chargesTaxData: {
                                      child_taxes: [
                                        {
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        },
                                      ],
                                    },
                                  });
                                  this.setState({
                                    selectedTenant: value,
                                    selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.it_id,
                                    tenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_store_id,
                                    sellerName: '',
                                    sellerGst: '',
                                    tenantSellerId: '',
                                    vendorAddress: '',
                                    finishedGoods: [{
                                      key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                                    }],
                                    bomByProducts: [],
                                    extraCharges: [],
                                    bomLines: [],
                                    productionRoutes: [],
                                    currentTab: '/finished-goods',
                                    selectedTenantSeller: '',
                                    grnTableData: [{
                                      key: uuidv4(),
                                      asset1: '',
                                      product_sku_name: '',
                                      quantity: '',
                                      selling_price: '',
                                      taxId: '',
                                      lot: '',
                                      product_sku_id: null,
                                      product_sku_info: null,
                                      child_taxes: [{
                                        tax_amount: 0,
                                        tax_type_name: '',
                                      }],
                                      // lineCustomFields: cfGoodReceivingNotesLine
                                      lineCustomFields: visibleLineCfs,
                                    }],
                                    paymentTerms: 0,
                                    paymentRemarks: '',
                                    gstNumber: '',
                                    chargeData: copychargeData,
                                    purchaseAccount: null,
                                  });
                                }}
                                showAll={false}
                                title="Location"
                                customStyle={{ height: '28px', border: 'none' }}
                                labelClassName="form__input-row__label"
                                inputClassName="form__input-row__input"
                                containerClassName="form__input-row"
                                noDropdownAlign
                                placeholder="Select Business Unit"
                                includedTenants={Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE)}
                                // disabled={selectedMO}
                                disabled={match?.params?.grnId || selectedPoForGrn || selectedPo}
                              />
                            </div>}
                          {/* vendor selector */}
                          {(match?.params?.grnId || selectedPoForGrn?.po_id) ? (
                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text
                                  text="Vendor"
                                  className="form__input-row__label"
                                />
                                <div className="form__input-row__input">
                                  <H3Text
                                    text={selectedPoForGrn ? selectedPoForGrn?.seller_info?.seller_name : selectedGRN?.tenant_seller_info?.seller_name || selectedPoForGrn?.seller_info?.seller_name}
                                    style={{ padding: '7px' }}
                                  />
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="ant-col-md-6">
                              <SelectSellerV2
                                selectedSeller={selectedTenantSeller}
                                onChange={(value) => {

                                  if (grnTypeValue === 'ADHOC') {
                                    const docCf = CustomFieldHelpers.postCfStructure(cfGoodReceivingNotesDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
                                    const vendorCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
                                    const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, vendorCf) || [];
                                    this.setState({
                                      cfGoodReceivingNotesDoc: mergedCf,
                                      paymentRemarks: value?.seller_info?.default_payment_terms?.remark || '',
                                      paymentTerms: value?.seller_info?.default_payment_terms?.due_days || 0,
                                    });
                                  }

                                  this.setState({
                                    selectedSeller: value,
                                    selectedTenantSeller: value?.tenant_seller_id,
                                    gstNumber: value?.seller_info?.gst_number,
                                    selectedPoValue: [],
                                    vendorAddress: grnTypeValue === 'ADHOC' || grnTypeValue === 'MULTIPO' ? value?.seller_info?.office_address_details : null,
                                    selectedPo: null,
                                    selectedPoJson: null,
                                    shippingAddress: grnTypeValue === 'MULTIPO' ? user?.tenant_info?.default_shipping_address_info : null,
                                    tenantDepartmentId: grnTypeValue !== 'ADHOC' ? user?.tenant_info?.default_store_id : tenantDepartmentId,
                                    grnTableData: (grnTypeValue === 'ADHOC') ? [
                                      {
                                        key: uuidv4(),
                                        child_taxes: [{
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        }],
                                        lineCustomFields: cfGoodReceivingNotesLine,
                                      }] : [],
                                    isAutomaticConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
                                    selectedCurrencyID: value?.org_currency_id,
                                    selectedCurrencyName: value?.currency_info,
                                    currencyConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? value?.currency_info?.automatic_conversion_rate : value?.currency_info?.conversion_rate,
                                    paymentRemarks: value?.seller_info?.default_payment_terms?.remark || '',
                                    paymentTerms: value?.seller_info?.default_payment_terms?.due_days || 0,
                                  });
                                  const excludePo = grnTypeValue === 'MULTIPO' ? true : false;
                                  getInventoryLocations(grnTypeValue !== 'ADHOC' ? user?.tenant_info?.default_store_id : tenantDepartmentId, '', null, true, null, null, () => { });
                                  // getPurchaseOrders(user?.tenant_info?.org_id, selectedTenant, value?.tenant_seller_id, '', '', '', '', 'ISSUED', 1, 30, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', excludePo, excludePo);
                                  if (selectedTenant) {
                                    getPurchaseOrdersV2({
                                      query: {
                                        org_id: user?.tenant_info?.org_id,
                                        tenant_id: selectedTenant,
                                        tenant_seller_id: value?.tenant_seller_id,
                                        status: 'ISSUED',
                                        page: 1,
                                        limit: 30,
                                        exclude_job_works_po: excludePo,
                                        exclude_subcontractor_po: excludePo,
                                      },
                                    });
                                  }
                                }}
                                containerClass="orgInputContainer form__input-row"
                                inputClassName={`form__input-row__input ${(formSubmitted && !selectedSeller) ? 'form__input-row__input-error' : ''}`}
                                labelClassName="form__input-row__label"
                                disabled={createGRNLoading || updateGRNLoading || match?.params?.grnId || selectedPoForGrn?.po_id}
                                showAddVendor
                                tenantId={selectedTenant}
                              />
                            </div>
                          )}
                          {/* purchase order selector */}
                          {((match?.params?.grnId || selectedPoForGrn?.po_id) && (selectedPurchaseOrder?.po_number || selectedPoForGrn?.po_number)) ? (
                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text
                                  text="Purchase Order"
                                  className="form__input-row__label"
                                />
                                <div className="form__input-row__input">
                                  <H3Text
                                    text={`${selectedPurchaseOrder?.po_number || selectedPoForGrn?.po_number} `}
                                    style={{ padding: '7px' }}
                                  />
                                </div>
                              </div>
                            </div>
                          ) : ((grnTypeValue === 'Purchase Order' || grnTypeValue === 'MULTIPO') ?
                            (
                              <div className="ant-col-md-6">
                                <div className="form__input-row">
                                  <div className="form__input-row__label">
                                    Select Order
                                  </div>
                                  <div>
                                    <SelectOrdersForGRN
                                      disabled={!selectedTenantSeller || createGRNLoading || updateGRNLoading}
                                      selectedPoValue={selectedPoValue}
                                      mode={grnTypeValue === 'MULTIPO' ? 'multiple' : 'single'}
                                      onChange={grnTypeValue === 'MULTIPO' ? handleMultiPoChange : handlePoChange}
                                      purchaseOrders={purchaseOrdersV2}
                                      isDataMaskingPolicyEnable={isDataMaskingPolicyEnable}
                                      isHideCostPrice={isHideCostPrice}
                                      MONEY={this.props.MONEY}
                                      grnTypeValue={grnTypeValue}
                                      selectedTenantSeller={selectedTenantSeller}
                                      selectedTenant={selectedTenant}
                                      loading={getPurchaseOrdersV2Loading || createGRNLoading || updateGRNLoading}
                                      getPurchaseOrdersV2={getPurchaseOrdersV2}
                                      inputClassName={`orders-selector ${formSubmitted && !selectedPoValue ? 'form__input-row__input-error' : ''}`}
                                    />
                                    {grnTypeValue === 'Purchase Order' ? (
                                      selectedPo && (
                                        <H3Text
                                          text="View Purchase Order"
                                          className="create-grn__view-po-button_new"
                                          onClick={() => {
                                            const win = window.open(`/approval?type=po&id=${selectedPo?.po_id}`, '_blank');
                                            win.focus();
                                          }}
                                        />
                                      )
                                    ) : (
                                      selectedPoValue?.length > 0 && (
                                        <H3Text
                                          text="View Purchase Order"
                                          className="create-grn__view-po-button_new"
                                          onClick={() => {
                                            this.setState({ openDrawerOfMultipleSelectedPos: true });
                                          }}
                                        />
                                      ))}
                                  </div>
                                </div>
                              </div>
                            )
                            : '')}
                          {/* department selector */}
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text="Department" className="form__input-row__label" />
                              <div className="form__input-row__input">
                                <SelectDepartment
                                  hideTitle
                                  tenantId={selectedTenant || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id || selectedGRN?.tenant_id}
                                  selectedDepartment={tenantDepartmentId}
                                  noDropdownAlign
                                  onChange={(value) => {
                                    this.setState({
                                      tenantDepartmentId: value?.tenant_department_id,
                                    });

                                    // adding new logic when we change dep all the grn lines become empty so now updating only batches location_id and location_path
                                    getInventoryLocations(value?.tenant_department_id, '', null, true, null, null, (locationData) => {
                                      const newLocationData = locationData?.inventory_location?.[0];
                                      if (newLocationData) {
                                        const updatedGrnTableData = grnTableData?.map((item) => {
                                          // Update the selectedBatch details
                                          const updatedSelectedBatch = {
                                            ...item.selectedBatch,
                                            inventory_location_id: newLocationData.inventory_location_id,
                                            inventory_location_path: newLocationData.inventory_location_path,
                                          };
                                          // Update the first available batch details
                                          const updatedAvailableBatches = item?.available_batches?.map((batch, index) => {
                                            if (index === 0) {
                                              return {
                                                ...batch,
                                                inventory_location_id: newLocationData.inventory_location_id,
                                                inventory_location_path: newLocationData.inventory_location_path,
                                              };
                                            }
                                            return batch;
                                          });
                                          // Update the first multiple batch info
                                          const updatedMultipleBatchInfo = item?.multiple_batch_info?.map((batch, index) => {
                                            if (index === 0) {
                                              return {
                                                ...batch,
                                                inventory_location_id: newLocationData.inventory_location_id,
                                                inventory_location_path: newLocationData.inventory_location_path,
                                              };
                                            }
                                            return batch;
                                          });
                                          return {
                                            ...item,
                                            selectedBatch: updatedSelectedBatch,
                                            available_batches: updatedAvailableBatches,
                                            multiple_batch_info: updatedMultipleBatchInfo,
                                          };
                                        });
                                        // Update the state with the updated grnTableData
                                        this.setState({
                                          grnTableData: updatedGrnTableData,
                                          inventoryLocationId: newLocationData.inventory_location_id,
                                          inventoryLocationPath: newLocationData.inventory_location_path,
                                        });
                                      }
                                    });
                                  }}
                                  tenentLevelDepartment
                                  emptyNotAllowed
                                  customStyle={{
                                    border: '1px solid rgba(68, 130, 218, 0.25)',
                                    borderRadius: '4px',
                                    height: '28px',
                                    padding: '0px',
                                    marginBottom: '10px',
                                  }}
                                  loading={createGRNLoading || updateGRNLoading}
                                  disabled={createGRNLoading || updateGRNLoading || selectedPoForGrn || selectedGRN || selectedPo}
                                  labelClassName="mo-form__input-row__label"
                                  inputClassName="orgFormInput input"
                                />
                              </div>
                            </div>
                          </div>
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                text="GST Number"
                                className="form__input-row__label"
                              />
                              <H3FormInput
                                name="GST Number"
                                type="text"
                                disabled={
                                  createGRNLoading
                                  || updateGRNLoading
                                  || accountingGSTTransactionAsPerMaster
                                }
                                containerClassName="orgInputContainer form__input-row__input"
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput input"
                                placeholder=""
                                onChange={(event) => {
                                  this.setState({ gstNumber: event.target.value });
                                }}
                                value={gstNumber}
                              />
                            </div>
                          </div>
                          {/* vendor invoice number  */}
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                text="Vendor Invoice#"
                                className="form__input-row__label"
                                required={user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory}
                              />
                              {/* <SettingsInfo
                                settingName="Keep invoice number of the vendor mandatory"
                                path="/admin/configuration?tab=%2Fadmin-config%2Fgoods-receiving&subTab=%2Fadmin-config%2Fgoods-receiving%2Fgeneral"
                              /> */}
                              <H3FormInput
                                name="invoice number"
                                type="text"
                                containerClassName={'orgInputContainer form__input-row__input'}
                                labelClassName="orgFormLabel"
                                inputClassName={`orgFormInput input ${(formSubmitted && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory && !invoiceNumber) ? 'form__input-row__input-error' : ''}`}
                                placeholder=""
                                onChange={(event) => {
                                  this.setState({ invoiceNumber: event.target.value });
                                }}
                                value={invoiceNumber}
                              />
                            </div>
                          </div>
                          {/* grn date */}
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <div className="form__input-row__label">
                                GRN Date
                                <span style={{ color: 'red' }}>{'  *'}</span>
                              </div>
                              <div className={`form__input-row__input ${(formSubmitted && !grnDate) ? 'form__input-row__input-error' : ''}`}>
                                <DatePicker
                                  value={dayjs(grnDate, 'DD-MM-YYYY')}
                                  onChange={(value) => {
                                    this.setState({ grnDate: value });
                                  }}
                                  disabledDate={
                                    (d) => !d
                                      || (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.allow_future_date_grn ? null : d.isAfter(dayjs().add(1, 'days').format('YYYY-MM-DD')))
                                      || (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.allow_past_date_grn ? null : d.isSameOrBefore(dayjs().format('YYYY-MM-DD')))
                                  }
                                  disabled={createGRNLoading || updateGRNLoading}
                                  format="DD-MM-YYYY"
                                  allowClear={false}
                                  style={{
                                    border: '1px solid rgba(68, 130, 218, 0.2)',
                                    borderRadius: '2px',
                                    height: '28px',
                                    padding: '1px 3px',
                                    width: '100%',
                                    background: 'white',
                                    marginBottom:
                                      formSubmitted && !grnDate ? '0px' : '10px',
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                text="Invoice Date"
                                className="form__input-row__label"
                              />
                              <div className={`form__input-row__input ${(formSubmitted && !invoiceDate) ? 'form__input-row__input-error' : ''}`}>
                                <DatePicker
                                  value={invoiceDate}
                                  onChange={(value) => {
                                    this.setState({ invoiceDate: value });
                                  }}
                                  disabled={createGRNLoading || updateGRNLoading}
                                  format="DD-MM-YYYY"
                                  style={{
                                    // border: (formSubmitted && !deliveryDate && deliveryDateMandatory) ? '1px solid red' : '1px solid rgba(68, 130, 218, 0.2)',
                                    borderRadius: '2px',
                                    height: '28px',
                                    padding: '1px 3px',
                                    width: '100%',
                                    background: 'white',
                                    // marginBottom: formSubmitted && !deliveryDate ? '0px' : '10px',
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                text="Due Date"
                                className="form__input-row__label"
                              />
                              <div className={`form__input-row__input ${(formSubmitted && !dueDate) ? 'form__input-row__input-error' : ''}`}>
                                <DatePicker
                                  value={dueDate}
                                  onChange={(value) => {
                                    this.setState({ dueDate: value });
                                  }}
                                  disabled={createGRNLoading || updateGRNLoading}
                                  format="DD-MM-YYYY"
                                  style={{
                                    border: (formSubmitted && !dueDate) ? '1px solid red' : '1px solid rgba(68, 130, 218, 0.2)',
                                    borderRadius: '2px',
                                    height: '28px',
                                    padding: '1px 3px',
                                    width: '100%',
                                    background: 'white',
                                    marginBottom: formSubmitted && !dueDate ? '0px' : '10px',
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          {user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled && (
                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text text="Purchase Account" className="form__input-row__label" required />
                                <div className={`form__input-row__input ${formSubmitted && !purchaseAccount ? 'form__input-row__input-error' : ''}`}>
                                  <PRZSelect
                                    value={purchaseAccount}
                                    onChange={(value) => {
                                      this.setState({ purchaseAccount: value });
                                    }}
                                    loading={createGRNLoading || updateGRNLoading}
                                    disabled={createGRNLoading}
                                  >
                                    {purchaseAccountList?.map((item) => (
                                      <Option key={item.purchase_account_name} value={item.purchase_account_name}>
                                        {' '}
                                        {item.purchase_account_name}
                                      </Option>
                                    ))}
                                  </PRZSelect>

                                </div>
                              </div>
                            </div>
                          )}
                          {this.getLineTotals().totalAmount >= 50000 && user?.tenant_info?.country_code === 'IN'
                            ? (
                              <React.Fragment>
                                <div className="ant-col-md-6">
                                  <div className="form__input-row">
                                    <H3Text
                                      text="e-Way Bill number"
                                      className="form__input-row__label"
                                      required={user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory}
                                    />
                                    <H3FormInput
                                      name="Ewaybill number"
                                      type="text"
                                      containerClassName={`orgInputContainer form__input-row__input ${(formSubmitted && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory && !ewayBillNumber) ? 'form__input-row__input-error' : ''}`}
                                      labelClassName="orgFormLabel"
                                      inputClassName="orgFormInput input"
                                      onChange={(event) => this.setState({ ewayBillNumber: event.target.value })}
                                      maxlength="100"
                                      value={ewayBillNumber}
                                      disabled={createGRNLoading}
                                    />
                                  </div>
                                </div>
                                <div className="ant-col-md-6">
                                  <div className="form__input-row">
                                    <H3Text
                                      text="e-Way Bill Attachment"
                                      className="form__input-row__label"
                                      required={user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory}
                                    />
                                    <Upload
                                      action={Constants.UPLOAD_FILE}
                                      // listType="picture-card"
                                      fileList={ewayBillList}
                                      disabled={createGRNLoading}
                                      multiple
                                      onChange={(ewayBillListData) => {
                                        this.setState({
                                          ewayBillList: ewayBillListData?.fileList?.map((item) => ({
                                            ...item,
                                            url: item?.response?.response?.location || item?.url,
                                          })),
                                        });
                                      }}
                                    >
                                      {ewayBillList?.length >= 1 ? null : uploadButtonFormLevel}
                                    </Upload>
                                  </div>
                                </div>
                              </React.Fragment>
                            ) : <React.Fragment />}
                          <div className="ant-col-md-6">
                            <div className="form__input-row ">
                              <div className="form__input-row__label">
                                Labels
                              </div>
                              <TagSelector
                                hideTitle
                                entityType="GRN"
                                selectedTags={selectedTags}
                                isMultiple
                                showSearch
                                onChange={(value) => {
                                  this.setState({ selectedTags: value });
                                }}
                                placeholder="Select Tags"
                                isForm
                                containerWrapper="form__input-row__input"
                                disabled={createGRNLoading || updateGRNLoading}
                                maxTagCount="responsive"
                              />
                            </div>
                          </div>
                          {!isApInvoiceEnabled && (<div className="ant-col-md-6">
                            <SelectPaymentTerm
                              selectedPaymentTerm={paymentTerms}
                              onChange={(value) => {
                                this.setState({ paymentTerms: value?.due_days });
                              }}
                              callback={(value) => {
                                this.setState({ paymentTerms: Number(value) });
                              }}
                              containerClassName="orgInputContainer form__input-row"
                              inputClassName=" form-seller__selector form__input-row__input"
                              labelClassName="form__input-row__label"
                              showError={formSubmitted && !paymentTerms}
                              disabled={
                                createGRNLoading || updateGRNLoading
                              }
                              showAddPaymentTerm
                              placeholder="Select Payment Term"
                            />
                          </div>)}
                          {!isApInvoiceEnabled && (<div className="ant-col-md-6">
                            <SelectPaymentRemark
                              selectedPaymentRemark={paymentRemarks}
                              onChange={(value) => {
                                this.setState({ paymentRemarks: value?.message });
                              }}
                              callback={(value) => {
                                this.setState({ paymentRemarks: value });
                              }}
                              containerClassName="orgInputContainer form__input-row"
                              inputClassName=" form-seller__selector form__input-row__input"
                              labelClassName="form__input-row__label"
                              showError={formSubmitted && !paymentRemarks}
                              disabled={
                                createGRNLoading || updateGRNLoading
                              }
                              showAddPaymentRemark
                              placeholder="Select Payment Term"
                            />
                          </div>)}
                          <CustomFieldV3
                            customFields={cfGoodReceivingNotesDoc}
                            formSubmitted={formSubmitted}
                            customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                            wrapperClassName="ant-col-md-6"
                            containerClassName="form__input-row"
                            labelClassName="form__input-row__label"
                            inputClassName="form__input-row__input"
                            errorClassName="form__input-row__input-error"
                            disableCase={createGRNLoading || updateGRNLoading
                              || (grnTypeValue === 'Purchase Order' && !selectedPoValue && !this.props?.selectedPoForGrn?.po_id)
                            }
                            hideTitle
                            isCarryForward={true}
                          />
                        </div>
                      </div>
                    </div>
                    {workOrder && !selectedPoForGrn?.subcontractor_bears_rm_cost && !selectedPoForGrn?.is_job_works_po && (
                      <div style={{ marginBottom: '10px' }}>
                        {!selectedPoForGrn?.is_invoice_issued ? (
                          <Alert
                            description="You have not issued the raw material for this work order. This may cause mismatch in the costing of goods receipt."
                            type="warning"
                            showIcon
                          />
                        ) : (
                          <Alert
                            description="You have already issued raw materials. The cost of raw materials issued for this work order will automatically be added in the landed cost of this received goods."
                            type="success"
                            showIcon
                          />
                        )}
                      </div>
                    )}
                    <div className="form__section">
                      <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                        <H3Text text="PART B" className="form__section-title" />
                        <div className="form__section-line" />
                      </div>
                      <div className="form__section-inputs">
                        <div className="ant-row">
                          {(grnTypeValue !== 'ADHOC') && (!match?.params?.grnId && !selectedPoForGrn?.po_id) && (
                            <div className="ant-col-md-8">
                              <div className="form__input-row">
                                <H3Text
                                  text="Delivery Address"
                                  className="form__input-row__label"
                                  required
                                />
                                <div className={`form__input-row__address__wrapper ${(formSubmitted && !shippingAddress) ? 'form__input-row__address-error' : ''} `}>
                                  <div className="form__input-row__address">
                                    {shippingAddress && (
                                      <div className="form__input-row__address-info">
                                        <div className="form__input-row__address-l1">
                                          {shippingAddress?.address1}
                                        </div>
                                        <div className="form__input-row__address-l2">
                                          {`${shippingAddress?.city}, ${shippingAddress?.state}, ${shippingAddress?.postal_code}`}
                                        </div>
                                      </div>
                                    )}
                                    {!shippingAddress && (
                                      <H3Text
                                        text="Select address.."
                                        className="form__input-row__address-placeholder"
                                      />
                                    )}
                                  </div>
                                  {formSubmitted && !shippingAddress && (
                                    <div className="input-error">
                                      *Please select delivery address
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                          {(!match?.params?.grnId) && (
                            <div className="ant-col-md-8">
                              <div className="form__input-row">
                                <H3Text
                                  text="Vendor's Address"
                                  className="form__input-row__label"
                                  required={grnTypeValue === 'ADHOC'}
                                />
                                <div className={`form__input-row__address__wrapper ${(formSubmitted && !vendorAddress && grnTypeValue === 'ADHOC') ? 'form__input-row__address-error' : ''} `}>
                                  <div className="form__input-row__address">
                                    {vendorAddress && (
                                      <div className="form__input-row__address-info">
                                        <div className="form__input-row__address-l1">
                                          {vendorAddress?.address1}
                                        </div>
                                        <div className="form__input-row__address-l2">
                                          {`${vendorAddress?.city}, ${vendorAddress?.state}, ${vendorAddress?.postal_code}, ${vendorAddress?.country}`}
                                        </div>
                                      </div>
                                    )}
                                    {!vendorAddress && (
                                      <H3Text
                                        text="Select address.."
                                        className="form__input-row__address-placeholder"
                                      />
                                    )}
                                    {(
                                      <div
                                        className="form__input-row__address-icon"
                                        onClick={() => {
                                          this.setState({
                                            selectedAddressType: 'SELLER',
                                            showAddressDrawer: true,
                                          });
                                        }}
                                      >
                                        <EditFilled />
                                        {' '}
                                        Update
                                      </div>
                                    )}
                                  </div>
                                  {formSubmitted && !vendorAddress && (
                                    <div className="input-error">
                                      *Please select vendor address
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                          <div className="ant-col-md-24">
                            <div className="form__input-row" style={{ marginTop: '10px' }}>
                              <Checkbox
                                disabled={createGRNLoading || updateGRNLoading}
                                checked={checkedRecipients}
                                onChange={() => {
                                  this.setState({
                                    checkedRecipients: !checkedRecipients,
                                  });
                                }}
                              />
                              <span
                                style={{
                                  fontWeight: '500',
                                  fontSize: '12px',
                                  marginLeft: '5px',
                                }}
                              >
                                Send automatic email when order is issued
                              </span>
                            </div>
                          </div>
                          {checkedRecipients && (
                            <div className="ant-col-md-8">
                              <div className="form__input-row__input">
                                <PRZSelect
                                  className={(formSubmitted && checkedRecipients && !toRecipients?.length) ? 'form__recipients__input-error' : ''}
                                  mode="tags"
                                  value={toRecipients}
                                  filterOption={false}
                                  maxTagCount="responsive"
                                  onChange={(value) => {
                                    const recipients = [];
                                    for (let i = 0; i < value?.length; i++) {
                                      if (Helpers.validateEmail(value[i])) {
                                        recipients.push(value[i]);
                                      }
                                    }
                                    this.setState({ toRecipients: recipients });
                                  }}
                                  disabled={createGRNLoading || updateGRNLoading}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="grn-form-lines-wrapper">
                  <GRNFormLines
                    selectedPoValue={selectedPoValue}
                    setSelectedPoValue={(value) => this.setState({ selectedPoValue: value })}
                    loading={createGRNLoading || updateGRNLoading || getCurrenciesLoading || getInventoryLocationsLoading}
                    grnTypeValue={grnTypeValue}
                    grnTableData={this.getGRNTableData(grnTableData)}
                    handleDelete={(value, id) => this.handleDelete(value, id)}
                    updateTableValue={(key, value, label) => this.updateTableValue(key, value, label)}
                    getClassName={(isFlexible, item) => this.getClassName(isFlexible, item)}
                    handleFullQuantity={(value) => this.handleFullQuantity(value)}
                    recordFullQuantity={() => this.recordFullQuantity()}
                    removeZeroQuantity={() => this.removeZeroQuantity()}
                    grnId={match?.params?.grnId}
                    isLineWiseDiscount={isLineWiseDiscount}
                    onChangeLineWiseDiscount={(value) => {
                      this.setState({ isLineWiseDiscount: value });
                    }}
                    updateGrnTableData={(value) => {
                      this.setState({ grnTableData: value });
                      this.updateLandedCost(value, chargeData, charge1Value);
                    }}
                    discountPercentage={(value) => this.setState({ discountPercentage: value })}
                    discountType={discountType}
                    showLineBatches={(value) => this.setState({ showLineBatches: value })}
                    availableBatches={(value) => this.setState({ availableBatches: value })}
                    selectedLineInfo={(value) => this.setState({ selectedLineInfo: value })}
                    handleProductChangeValue={(value, key) => this.handleProductChangeValue(value, key)}
                    handleProductChange={(value, key, productData) => this.handleProductChange(value, key, productData)}
                    addNewRow={() => this.addNewRow()}
                    toggleBatchInner={(batch, adjustmentRow) => this.toggleBatchInner(batch, adjustmentRow)}
                    allStock={allStock}
                    toggleAllStock={() => this.setState({ allStock: !allStock })}
                    toggleBatch={(batch, adjustmentRow) => this.toggleBatch(batch, adjustmentRow)}
                    selectedGRN={selectedGRN}
                    tenantDepartmentId={tenantDepartmentId}
                    selectedPoForGrn={selectedPoForGrn}
                    selectedPo={selectedPo}
                    customClassName={!!selectedPoForGrn}
                    selectedTenantSeller={selectedTenantSeller}
                    selectedCurrencyName={selectedCurrencyName}
                    isMultipleBatchModeEnabled={(value) => this.setState({ isMultipleBatchModeEnabled: value })}
                    isBatchValid={isBatchValid}
                    setIsBatchValid={(value) => this.setState({ isBatchValid: value })}
                    formSubmitted={formSubmitted}
                    setMultipleBatchValidation={(value) => this.setState({ multipleBatchValidation: value })}
                    multipleBatchValidation={multipleBatchValidation}
                    billFromState={this.getBillFromAddress()}
                    billToState={this.getBillToAddress()}
                    cfGoodReceivingNotesLine={cfGoodReceivingNotesLine}
                    customLineInputChange={(value, cfId, key, returnCfData) => this.customLineInputChange(value, cfId, key, returnCfData)}
                    visibleLineCfs={visibleLineCfs}
                    customFieldVisibilityChange={(visible, cfId) => {
                      this.customFieldVisibilityChange(visible, cfId);
                    }}
                    onBulkUploadBatch={(batchData, id) => this.onBulkUploadBatch(batchData, id)}
                    inventoryLocationId={inventoryLocationId}
                    inventoryLocationPath={inventoryLocationPath}
                    selectedTenant={selectedTenant || selectedGRN?.tenant_info?.tenant_id || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id}
                    isCarryForward={selectedPoForGrn || selectedPoValue}
                    handleMultiProductChange={this.handleMultiProductChange}
                    updateBatchCfs={(value, cfId, item) => this.updateBatchCfs(value, cfId, item)}
                    updateBatchCfsForMultiBatchMode={(value, cfId, item) => this.updateBatchCfsForMultiBatchMode(value, cfId, item)}
                    selectedSellerInfo={selectedSeller?.seller_info || selectedPoForGrn?.seller_info}
                    isApInvoiceEnabled={isApInvoiceEnabled}
                  />
                </div>
                <div className="form__data-wrapper">
                  <div className="ant-row">
                    <div className="ant-col-md-24">
                      <div className="flex-display">
                        {((user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.enable_adhoc_po_grn && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) || grnTypeValue === 'ADHOC') && addNewAdhocLineInPoToGrnAllowed && (
                          <div
                            className={`new-row-button ${!!grnTableData?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)) ? 'new-row-button__disabled' : ''}`}
                            onClick={() => {
                              if (!grnTableData?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id))) this.addNewRow()
                            }}
                          >
                            <FontAwesomeIcon icon={faPlusCircle} />
                            &nbsp;New Item
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="ant-col-md-12">
                      <div className="form__data-tc">
                        <label className="orgFormLabel">
                          Additional Remarks
                        </label>
                        <textarea
                          className="orgFormInput"
                          rows="4"
                          onChange={(event) => {
                            this.setState({ terms: event.target.value });
                          }}
                          disabled={createGRNLoading || updateGRNLoading}
                          value={terms}
                          style={{
                            height: '100px',
                          }}
                        />
                      </div>
                      {!!selectedTenantTallyIntegrationId && <div className="form__data-tc">
                        <label className="orgFormLabel">
                          Narration
                        </label>
                        <textarea
                          className="orgFormInput"
                          rows="4"
                          onChange={(event) => {
                            this.setState({ narration: event.target.value });
                          }}
                          disabled={createGRNLoading || updateGRNLoading}
                          value={narration}
                          style={{
                            minHeight: '40px',
                            height: '60px',
                          }}
                        />
                      </div>}
                      <div className="form__data-attachment">
                        <label className="orgFormLabel">Attachment(s)</label>
                        <Upload
                          action={Constants.UPLOAD_FILE}
                          listType="picture-card"
                          fileList={fileList || []}
                          disabled={createGRNLoading || updateGRNLoading}
                          multiple
                          onChange={(fileListData) => {
                            this.setState({
                              fileList: fileListData?.fileList?.map((item) => ({
                                ...item,
                                url:
                                  item?.response?.response?.location || item?.url,
                              })),
                            });
                          }}
                        >
                          {fileList?.length >= 20 ? null : uploadButtonForUploadingAttachments}
                        </Upload>
                      </div>
                    </div>
                    <div className="ant-col-md-12">
                      <div className={isApInvoiceEnabled ? 'display-none' : 'form-calculator__wrapper'}>
                        <div className="form-calculator">
                          <div className="form-calculator__field">
                            <H3Text
                              text="Sub Total"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={this.props.MONEY((this.getLineTotals().totalAmount || 0), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage={'You don\'t have access to view sub total amount'}
                            />
                          </div>
                          {!isLineWiseDiscount && (
                            <div className="form-calculator__field">
                              <H3Text
                                text="Discount"
                                className="form-calculator__field-name"
                              />
                              <H3Text
                                text={this.props.MONEY((this.getLineTotals().totalDiscount || 0), selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                              />
                            </div>
                          )}
                          {isLineWiseDiscount && selectedCurrencyName && (
                            <div className="form-calculator__field">
                              <H3Text
                                text="Discount"
                                className="form-calculator__field-name"
                              />
                              <div
                                className="form-calculator__field-value"
                                style={{ display: 'flex' }}
                              >
                                <div style={{ width: '112px' }}>
                                  <H3FormInput
                                    value={discountPercentage}
                                    type="number"
                                    containerClassName={`${formSubmitted
                                      && Number(discountPercentage) <= 0
                                      ? 'form-error__input'
                                      : ''
                                      }`}
                                    labelClassName="orgFormLabel"
                                    inputClassName="orgFormInput"
                                    onChange={(e) => {
                                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                                      copyData.map((item) => {
                                        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

                                        const discountValue = discountType === 'Percent' ? parseFloat(e.target.value || 0) : ((item.quantity * item.offer_price) / parseFloat(totalValue)) * parseFloat(e.target.value || 0);

                                        const taxableValue = discountType === 'Percent'
                                          ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
                                          : Math.max(item.quantity * item?.offer_price - discountValue, 0);

                                        item.discount = discountValue;
                                        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes;
                                        return grnTableData;
                                      });
                                      this.setState({ grnTableData: copyData, discountPercentage: parseFloat(e.target.value || 0) });
                                    }}
                                  />
                                </div>
                                <div className="form-calculator__discount-type">
                                  <PRZSelect
                                    value={discountType}
                                    onChange={(value) => {
                                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                                      copyData.map((item) => {
                                        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

                                        const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.offer_price) / parseFloat(totalValue)) * Number(discountPercentage);

                                        const taxableValue = value === 'Percent'
                                          ? (item?.quantity * item?.offer_price) * (1 - discountValue / 100)
                                          : Math.max(item.quantity * item?.offer_price - discountValue, 0);

                                        item.lineDiscountType = value;
                                        item.discount = discountValue;
                                        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes;
                                        return item;
                                      });
                                      this.setState({
                                        grnTableData: copyData,
                                        discountType: value,
                                      });
                                    }}
                                  // disabled
                                  >
                                    <Option key="Amount" value="Amount">
                                      {`${selectedCurrencyName?.currency_symbol}`}
                                    </Option>
                                    <Option key="Percent" value="Percent">
                                      %
                                    </Option>
                                  </PRZSelect>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="form-calculator__field">
                            <div
                              className="form-calculator__field-name"
                              style={{ display: 'flex', alignItems: 'center' }}
                            >
                              <H3Text
                                text={charge1Name}
                                style={{ marginRight: '10px' }}
                              />
                              {freightTax && (
                                <div style={{
                                  color: '#2d7df7',
                                  fontWeight: '400',
                                  fontSize: '12px',
                                }}
                                >
                                  {`tax@${freightTax}%`}
                                </div>
                              )}
                            </div>
                            <div
                              className="form-calculator__field-value"
                              style={{ display: 'flex' }}
                            >
                              <div style={{ width: '112px' }}>
                                <H3FormInput
                                  value={charge1Value}
                                  type="number"
                                  containerClassName={`${formSubmitted && Number(charge1Value) < 0
                                    ? 'form-error__input'
                                    : ''
                                    }`}
                                  labelClassName="orgFormLabel"
                                  inputClassName="orgFormInput"
                                  onChange={(e) => {
                                    this.setState({
                                      charge1Value: e.target.value,
                                      freightTaxData: {
                                        ...freightTaxData,
                                        child_taxes: Helpers.computeTaxation(e.target.value, freightTaxInfo, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes
                                      },
                                    });
                                    this.updateLandedCost(grnTableData, chargeData, e.target.value);
                                  }}
                                  disabled={!isReceivedQty}
                                />
                              </div>
                              <div className={isVendorOverseas ? 'display-none' : ''}>
                                <FreightTaxInput
                                  freightTaxId={freightTaxId}
                                  openFreightTax={openFreightTax}
                                  sacCode={freightSacCode}
                                  setOpenFreightTax={(value) => this.setState({ openFreightTax: value })}
                                  setFreightTaxData={(value) => this.setState({
                                    freightTaxId: !value ? 'Not Applicable' : value?.tax_id,
                                    freightTax: !value ? null : value?.tax_value,
                                    freightTaxInfo: !value ? null : value,
                                    freightTaxData: !value ? {
                                      child_taxes: [
                                        {
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        },
                                      ],
                                    } : {
                                      ...value,
                                      child_taxes: Helpers.computeTaxation(charge1Value, value, this.getBillFromAddress(), this.getBillToAddress())?.tax_info?.child_taxes
                                    },
                                  })}
                                  setSacCode={(value) => this.setState({ freightSacCode: value })}
                                />
                              </div>
                            </div>
                          </div>

                          {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}

                          <div className="form-calculator__field">
                            <H3Text
                              text="Taxable Amount"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={this.props.MONEY((this.getLineTotals().totalBase || 0), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage={'You don\'t have access to view taxable amount'}
                            />
                          </div>

                          {!isVendorOverseas && (grnTypeValue === 'Purchase Order' ? grnTableData?.length > 0 : true) && grnTableData?.[0]?.child_taxes?.[0]?.tax_type_name && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...grnTableData, freightTaxData, ...chargeData?.flatMap((charge) => charge?.chargesTaxData)]))?.map((tax, i) => (
                            <Fragment key={i}>
                              <div className="form-calculator__field">
                                <H3Text
                                  text={tax?.tax_type_name}
                                  className="form-calculator__field-name"
                                />
                                <H3Text
                                  text={MONEY((tax?.tax_amount || '0'), selectedCurrencyName?.currency_code)}
                                  className="form-calculator__field-value"
                                  hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                                  popOverMessage={`You don't have access to view ${tax?.tax_type_name?.toLowerCase()}`}
                                />
                              </div>
                            </Fragment>
                          ))}

                          {user?.tenant_info?.global_config?.settings?.enable_tds_tcs && user?.tenant_info?.country_code === 'IN' && (selectedSeller ? selectedSeller?.seller_info?.seller_type !== 'OVERSEAS' : selectedPoForGrn?.seller_info?.seller_type !== 'OVERSEAS') && (
                            <div className="form-calculator__field">
                              <div
                                className="form-calculator__field-name"
                                style={{
                                  display: 'flow',
                                }}
                              >
                                <Radio.Group
                                  disabled={createGRNLoading || updateGRNLoading}
                                  onChange={(event) => {
                                    this.setState({
                                      taxTypeName: event.target.value,
                                      taxTypeId: '',
                                      taxTypeInfo: null,
                                    });
                                  }}
                                  value={taxTypeName}
                                >
                                  <Radio value="TDS">TDS</Radio>
                                  <Radio value="TCS">TCS</Radio>
                                </Radio.Group>
                                <SelectTaxType
                                  containerClassName="orgInputContainer"
                                  selectedTaxType={taxTypeId}
                                  disabled={createGRNLoading || updateGRNLoading}
                                  onChange={(value) => {
                                    this.setState({
                                      taxTypeId: value?.tax_id,
                                      taxTypeInfo: value,
                                      taxTypeName: value?.tax_type_name,
                                      taxType: value?.tax_type,
                                    });
                                  }}
                                  taxTypeName={taxTypeName}
                                  customStyle={{
                                    width: '220px',
                                    backgroundColor: 'white',
                                  }}
                                />
                              </div>

                              {taxTypeName === 'TCS' ? (
                                <H3Text
                                  text={MONEY((this.getLineTotals().totalTcs || '0'), selectedCurrencyName?.currency_code)}
                                  className="form-calculator__field-value"
                                />
                              ) : (
                                <H3Text
                                  text={MONEY((this.getLineTotals().totalTds || '0'), selectedCurrencyName?.currency_code)}
                                  className="form-calculator__field-value"
                                />
                              )}
                            </div>
                          )}
                          {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}
                          <div
                            className="new-charge-row-button"
                            onClick={() => this.addNewChargesRow()}
                          >
                            <span className="new-charge-row-button__icon">
                              <PlusCircleFilled />
                            </span>
                            <div>Add Charges</div>
                          </div>
                          {user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method !== 'NO_ROUND_OFF' && (
                            <div className="form-calculator__field">
                              <H3Text
                                text="Round Off"
                                className="form-calculator__field-name"
                              />
                              <Tooltip
                                title={`Round Off method for GRN is set to ${user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method?.replace(/_/g, ' ')?.toProperCase()}`}
                              >
                                <div style={{ cursor: 'pointer', }}>
                                  <FontAwesomeIcon icon={faCircleInfo} size='lg' style={{ color: '#2D7DF7', }} />
                                </div>
                              </Tooltip>
                              <H3Text
                                text={`${Helpers.configuredRoundOff(this.getLineTotals().grnTotal, user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                                  Math.abs(Helpers.configuredRoundOff(this.getLineTotals().grnTotal, user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method)?.roundOff),
                                  selectedCurrencyName?.currency_code
                                )}`}
                                className="form-calculator__field-value"
                                hideText={
                                  isDataMaskingPolicyEnable && isHideCostPrice
                                }
                                popOverMessage={
                                  'You don\'t have access to view sub total'
                                }
                              />
                            </div>
                          )}
                          <div className="form-calculator__field form-calculator__field-total">
                            <H3Text
                              text="Grand Total"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={this.props.MONEY(Helpers.configuredRoundOff(this.getLineTotals().grnTotal || 0, user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.round_off_method)?.value, selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage={'You don\'t have access to view grand total amount'}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Fragment>
          )
          }
          <br />
          <br />
          <br />
          <div className="form__footer">
            {!!this.restrictMessage()?.length && (<RestrictedAccessMessage
              message={this.restrictMessage()}
            />)}
            {(!!selectedGRN) ? (
              <Fragment>
                {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
                  <PRZConfirmationPopover
                    title="Are you sure you want to update?"
                    content={
                      <Fragment>
                        <PRZText text="Reason" required />
                        <PRZInput
                          placeholder="Enter update reason"
                          value={updateDocumentReason}
                          onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                        />
                      </Fragment>
                    }
                    onConfirm={() => {
                      if (!createGRNLoading || !updateGRNLoading) {
                        this.handleCreateGRN(false, 'DRAFT');
                      }
                    }}
                    confirmButtonText="Confirm"
                    cancelButtonText="Back"
                    confirmDisabled={!updateDocumentReason}
                  >
                    <PRZButton
                      id="save-as-draft"
                      type="default"
                      wrapperStyle={{ marginRight: '10px' }}
                      buttonStyle={{
                        width: '130px',
                        height: '40px',
                        border: '1px solid #2d7df7',
                        color: '#2d7df7',
                      }}
                      isLoading={(createGRNLoading || updateGRNLoading) && buttonClick !== 'APPROVE'}
                      disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
                    >
                      Save as Draft
                    </PRZButton>
                  </PRZConfirmationPopover>
                )}
                {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
                  <PRZConfirmationPopover
                    title="Are you sure you want to update?"
                    content={
                      <Fragment>
                        <PRZText text="Reason" required />
                        <PRZInput
                          placeholder="Enter update reason"
                          value={updateDocumentReason}
                          onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                        />
                      </Fragment>
                    }
                    onConfirm={() => {
                      if (!createGRNLoading || !updateGRNLoading) {
                        this.handleCreateGRN(true, 'ISSUED');
                      }
                    }}
                    confirmButtonText="Confirm"
                    cancelButtonText="Back"
                    confirmDisabled={!updateDocumentReason}
                  >
                    <PRZButton
                      id="save-and-issue"
                      isLoading={(createGRNLoading || updateGRNLoading) && buttonClick === 'APPROVE'}
                      disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
                      buttonStyle={{ width: '130px', height: '40px' }}
                    >
                      Save and Issue
                    </PRZButton>
                  </PRZConfirmationPopover>
                )}
              </Fragment>
            ) : (
              <Fragment>
                {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
                  <PRZButton
                    id="save-as-draft"
                    type="default"
                    onClick={() => {
                      if (!createGRNLoading || !updateGRNLoading) {
                        this.handleCreateGRN(false, 'DRAFT');
                      }
                    }}
                    isLoading={(createGRNLoading || updateGRNLoading) && buttonClick !== 'APPROVE'}
                    disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
                    wrapperStyle={{ marginRight: '10px' }}
                    buttonStyle={{
                      width: '130px',
                      height: '40px',
                      border: '1px solid #2d7df7',
                      color: '#2d7df7',
                    }}
                  >
                    Save as Draft
                  </PRZButton>
                )}
                {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
                  <PRZButton
                    id="save-and-issue"
                    onClick={() => {
                      if (!createGRNLoading || !updateGRNLoading) {
                        this.handleCreateGRN(true, 'ISSUED');
                      }
                    }}
                    isLoading={(createGRNLoading || updateGRNLoading) && buttonClick === 'APPROVE'}
                    disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
                    buttonStyle={{ width: '130px', height: '40px' }}
                  >
                    Save and Issue
                  </PRZButton>
                )}
              </Fragment>
            )}
          </div>
          <H3Modal
            onClose={() => {
              getPurchaseOrderByIdSuccess(null);
              this.setState({ openPurchaseOrderModal: false });
            }}
            isOpen={openPurchaseOrderModal}
            title={`Purchase Order #${selectedPo ? selectedPo.po_id : ''}`}
            showHeader
          >
            <ViewPurchaseOrder
              selectedPo={selectedPo}
              handleClose={() => {
                getPurchaseOrderById(selectedPo?.po_id, selectedTenant, () => { });
              }}
            />
          </H3Modal>

          <Drawer
            open={showLineBatches}
            onClose={() => this.setState({ showLineBatches: false })}
            width="400px"
            destroyOnClose
          >
            <div className="custom-drawer__header-wrapper">
              <div className="custom-drawer__header" style={{ width: '355px' }}>
                <H3Text text={`${availableBatches?.length || '0'} Available Batches`} className="custom-drawer__title" />
                <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showLineBatches: false })} />
              </div>
            </div>
            <div className="batch-list__status-toggle">
              Show rejected batches
              <Switch style={{ marginLeft: '10px' }} onChange={() => { this.setState({ toggleRejectedBatches: !toggleRejectedBatches }); }} />
            </div>
            <BatchesList
              batches={toggleRejectedBatches ? availableBatches : availableBatches?.filter((record) => (record?.is_rejected_batch === null || record?.is_rejected_batch === false))}
              expiryDays={selectedLineInfo?.expiryDays}
              expiryDate={selectedLineInfo?.expiryDate}
              batchUom={selectedLineInfo?.batchUom}
              selectedBatch={selectedLineInfo?.selectedBatch}
              uomList={selectedLineInfo?.uomList}
              tenantProductId={selectedLineInfo?.tenantProductId}
              setSelectedBatch={(batch) => this.setSelectedBatch(batch)}
              onAddNewBatch={(batch, callback) => this.addNewBatch(batch, callback)}
              removeNewBatch={() => this.removeNewBatch()}
              prefillCostPrice={selectedLineInfo?.costPrice}
              prefillMrp={selectedLineInfo?.mrp}
              destDepartmentId={tenantDepartmentId}
              nextBatchCode={selectedLineInfo?.nextBatchCode}
            />
          </Drawer>
          <VendorRating
            selectedEntity={selectedGRN}
            selectedSeller={selectedSeller}
            openModal={showModal}
            callback={getHistory}
            SelectPO={selectedPoForGrn}
            grnId={latestGrnId}
          />
        </div >
        <Drawer
          open={showAddressDrawer}
          onClose={() => this.setState({ showAddressDrawer: false, selectedAddressType: '' })}
          width="360"
          destroyOnClose
        >
          <AddressSelector
            title="Vendor Address"
            addressType={selectedAddressType?.split('_')[0]}
            selectedAddressId={vendorAddress?.address_id}
            onAddressChange={(address) => {
              this.setState({
                vendorAddress: address,
                showAddressDrawer: false,
                grnTableData: grnTableData?.map((record) => ({
                  ...record,
                  child_taxes: grnTableData?.[0]?.product_sku_name ? Helpers.computeTaxation((record.quantity * record.offer_price) * (record.discount ? Number(100 - record.discount) / 100 : 1), record.taxInfo, this.getBillFromAddress(), address?.state)?.tax_info?.child_taxes : [{
                    tax_amount: 0,
                    tax_type_name: '',
                  }],
                })),
              });
            }}
            entityId={selectedSeller?.seller_id || selectedSellerId}
            entityType="SELLER"
            seller={selectedTenantSeller || selectedSeller}
            tenantId={selectedTenant || selectedGRN?.tenant_id || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id}
          />
        </Drawer>
        {
          grnTypeValue === 'MULTIPO' && <Drawer
            open={openDrawerOfMultipleSelectedPos}
            onClose={() => this.setState({ openDrawerOfMultipleSelectedPos: false })}
            width="300px"
            destroyOnClose
          >
            <div className="custom-drawer__header-wrapper">
              <div className="custom-drawer__header" style={{ width: '260px' }}>
                <H3Text text="View Purchase Orders" className="custom-drawer__title" />
                <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ openDrawerOfMultipleSelectedPos: false })} />
              </div>
            </div>
            {selectedPoValue?.map((po) => (
              <Card
                key={po?.key}
                onClick={() => window.open(`/approval?type=po&id=${po.value}`, '_blank')}
                className="po-card"
                bodyStyle={{ padding: '4px' }}
              >
                <div className="po-card__content">
                  <span className="po-card__label">{po?.label?.[0]}</span>
                  <strong className="po-card__amount">{po?.label?.[1]}</strong>
                </div>
              </Card>
            ))}
          </Drawer>
        }
      </Fragment >
    );
  }
}

const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, SellerReducers, GRNReducers, CFV2Reducers, InventoryLocationReducers, VendorRatingReducers, CurrenciesReducers, ExtraChargesReducers, TagReducers, GetPurchaseOrders, TaxReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  createGRNLoading: GRNReducers.createGRNLoading,
  getGRNByIdLoading: GRNReducers.getGRNByIdLoading,
  updateGRNLoading: GRNReducers.updateGRNLoading,
  selectedGRN: GRNReducers.selectedGRN,
  sellers: SellerReducers.sellers,
  purchaseOrders: PurchaseOrderReducers.purchaseOrders,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  cfV2DocGoodReceivingNotes: CFV2Reducers.cfV2DocGoodReceivingNotes,
  getDocCFV2GoodReceivingNotesLoading: CFV2Reducers.getDocCFV2GoodReceivingNotesLoading,
  inventoryLocations: InventoryLocationReducers.inventoryLocations,
  getVendorRatingSuccess: VendorRatingReducers.getVendorRatingSuccess,
  CurrenciesResults: CurrenciesReducers.CurrenciesResults,
  getCurrenciesLoading: CurrenciesReducers.getCurrenciesLoading,
  createTagLoading: TagReducers.createTagLoading,
  priceMasking: UserReducers.priceMasking,
  getInventoryLocationsLoading: InventoryLocationReducers.getInventoryLocationsLoading,
  purchaseOrdersV2: GetPurchaseOrders.data,
  getPurchaseOrdersV2Loading: GetPurchaseOrders.loading,
  taxesGroup: TaxReducers.taxesGroup,
  getTaxesLoading: TaxReducers.getTaxesLoading,
});

const mapDispatchToProps = (dispatch) => ({
  addGRN: (payload, action) => dispatch(GRNActions.createGRN(payload, action)),
  getTenantsConfiguration: (tenantId, callback) => dispatch(TenantActions.getTenantsConfiguration(tenantId, callback)),
  updateGRNStatus: (payload, action) => dispatch(GRNActions.updateGRNStatus(payload, action)),
  getSellers: (keyword, tenantId, page, limit, sellerId, callback) => dispatch(SellerActions.getSellers(keyword, tenantId, page, limit, sellerId, callback)),
  getPurchaseOrderById: (poId, tenantId, callback) => dispatch(PurchaseOrderActions.getPurchaseOrderById(poId, tenantId, callback)),
  getPurchaseOrderByIdSuccess: (selectedPo) => dispatch(PurchaseOrderActions.getPurchaseOrderByIdSuccess(selectedPo)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  updateGRN: (payload, action) => dispatch(GRNActions.updateGRN(payload, action)),
  getGRNById: (tenantId, grnId, callback) => dispatch(GRNActions.getGRNById(tenantId, grnId, callback)),
  getGRNByIdSuccess: (selectedGRN) => dispatch(GRNActions.getGRNByIdSuccess(selectedGRN)),
  getInventoryLocations: (tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable, tenantId, departmentId, callback) => dispatch(InventoryLocationActions.getInventoryLocations(tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable, tenantId, departmentId, callback)),
  getVendorRating: (orgId, callback) => dispatch(VendorRatingActions.getVendorRating(orgId, callback)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
  createTag: (payload, callback) => dispatch(TagActions.createTag(payload, callback)),
  uploadDocumentOCR: (payload, action) => dispatch(OCRActions.uploadDocumentOCR(payload, action)),
  getCharges: (orgId, entityName, callback) => dispatch(ExtraChargesActions.getCharges(orgId, entityName, callback)),
  getPurchaseOrdersV2: (payload, callback) => dispatch(GetPurchaseOrders.actions.request(payload, callback)),
  getTaxes: (orgId, page, limit, isActive, isGroup) => dispatch(TaxActions.getTaxes(orgId, page, limit, isActive, isGroup)),
});
export default connect(mapStateToProps, mapDispatchToProps)(withRouter(GRNForm));
