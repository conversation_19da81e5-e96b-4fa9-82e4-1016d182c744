import FormHelpers from '@Helpers/FormHelpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';

/**
 * Builds the grn_lines payload from the form's table data.
 * @param {object} data - The state of the GRN lines from the form.
 * @param {object} options - Configuration options for building the payload.
 * @param {boolean} options.isUpdate - True if it's an update operation.
 * @param {string} options.grnEntityType - The entity type of the GRN (e.g., 'GOOD_RECEIVING_NOTE').
 * @param {boolean} options.isMultipleBatchModeEnabled - Flag for multiple batch mode.
 * @param {string} options.tenantDepartmentId - The ID of the tenant department.
 * @param {string} options.grnTypeValue - The type of GRN (e.g., 'ADHOC').
 * @returns {Array} The formatted grn_lines array for the API payload.
 */
/**
 * Builds the final GRN payload for the API.
 * @param {object} state - The current state of the GRN form.
 * @param {Array} grnLines - The generated GRN lines from `buildGrnLinesPayload`.
 * @param {boolean} approve - Whether the GRN should be approved.
 * @param {object} options - Additional configuration.
 * @param {boolean} options.isUpdate - True if it's an update operation.
 * @param {object} options.user - The user object.
 * @param {function} options.getLineTotals - Function to calculate totals.
 * @returns {object} The final payload object for the API.
 */

/**
 * Validates the GRN form data before submission.
 * @param {object} state - The current state of the GRN form.
 * @returns {string[]} An array of error messages. Empty if valid.
 */
export const getGrnValidationErrors = (state) => {
  const errors = [];
  const { isMultipleBatchModeEnabled, data, checkedRecipients, toRecipients, cfGoodReceivingNotesDoc } = state;

  // Validation for multiple batch mode
  if (isMultipleBatchModeEnabled) {
    const hasBatchValidationErrors = data?.some((item) =>
      item?.multiple_batch_info?.some(
        (batch) => !batch?.custom_batch_number || !batch?.quantity || Number(batch?.quantity) <= 0
      )
    );

    const hasCustomFieldErrors = data?.some((item) =>
      item?.multiple_batch_info?.some((batch) =>
        batch?.custom_fields?.some((field) => {
          const hasNoValue = field?.fieldValue === undefined || field?.fieldValue === null || field?.fieldValue === '';
          return field.isActive && field.isRequired && hasNoValue;
        })
      )
    );

    if (hasBatchValidationErrors || hasCustomFieldErrors) {
      errors.push('Please enter all mandatory information for batches.');
    }
  }

  // Validation for email recipients
  if (checkedRecipients && (!toRecipients || toRecipients.length === 0)) {
    errors.push('Please provide an email address for notification purposes.');
  }

  // Validation for mandatory document-level custom fields
  const hasDocCfErrors = cfGoodReceivingNotesDoc?.some((customField) => {
    const isAttachment = customField?.fieldType === 'ATTACHMENT';
    const hasNoValue = isAttachment
      ? !customField?.fieldValue?.length
      : (customField?.fieldValue === undefined || customField?.fieldValue === null || customField?.fieldValue === '');
    return customField.isActive && customField.isRequired && hasNoValue;
  });

  if (hasDocCfErrors) {
    errors.push('Please fill all the mandatory inputs.');
  }

  return errors;
};

export const buildGrnPayload = (state, grnLines, approve, options) => {
  const { isUpdate, user, getLineTotals } = options;
  const { discountType, discountPercentage, isLineWiseDiscount, taxTypeInfo, taxTypeName, taxType, chargeData } = state;

  const getDocDiscount = () => {
    if (discountType === 'Percent') {
      return discountPercentage;
    }
    const { totalAmount } = getLineTotals();
    if (!totalAmount) return 0;
    return (discountPercentage / totalAmount) * 100;
  };

  const basePayload = {
    grn_lines: grnLines,
    status: approve ? 'ISSUED' : 'DRAFT',
    notification_recipients: state.toRecipients,
    is_automatic_notification_enabled: state.checkedRecipients,
    grn_date_time: state.grnDate,
    invoice_number: state.invoiceNumber || null,
    invoice_date: state.invoiceDate || null,
    seller_address_info: state.vendorAddress,
    tally_purchase_account: state.purchaseAccount,
    e_way_bill: state.ewayBillList?.map((att) => ({ url: att?.response?.response?.location || att?.url, type: att.type, name: att.name, uid: att.uid })) || [],
    e_way_bill_number: state.ewayBillNumber,
    grn_due_date: state.dueDate || null,
    custom_fields: CustomFieldHelpers.postCfStructure(state.cfGoodReceivingNotesDoc),
    other_charges: chargeData?.map((charge) => ({
      charge_name: charge?.charge_name,
      charge_amount: charge?.charge_amount,
      charge_type: '',
      charge_sac_code: charge?.chargesSacCode || null,
      tax_info: charge?.chargesTaxInfo || null,
      ledger_name: charge?.tallyLedgerName || null,
    })) || [],
    discount_percentage: getDocDiscount(),
    is_discount_in_percent: discountType === 'Percent',
    is_line_wise_discount: !isLineWiseDiscount,
    attachments: state.fileList?.map((att) => ({ url: att?.response?.response?.location || att?.url, type: att.type, name: att.name, uid: att.uid })) || [],
    remark: state.terms || '',
    tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
    tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? { ...taxTypeInfo } : null,
    tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
    tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? { ...taxTypeInfo } : null,
    conversion_rate: state.currencyConversionRate || state.selectedCurrencyInfo?.conversion_rate,
    org_currency_id: state.selectedCurrencyInfo?.org_currency_id,
    charge_1_name: state.charge1Name,
    charge_1_value: Number(state.charge1Value),
    transporter_id: state.transporterId,
    vehicle_number: state.vehicleNumber,
    freight_tax_id: state.freightTaxId === 'Not Applicable' ? null : state.freightTaxId,
    freight_tax_info: state.freightTaxInfo,
    freight_sac_code: state.freightSacCode,
    transporter_bill_number: state.transporterBillNumber,
    narration: state.narration || '',
    allow_grn_to_create_ap_invoice: user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled && user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active,
    payment_terms: [{
      advance_amount: 0,
      due_days: state.paymentTerms || 0,
      remark: state.paymentRemarks || '',
    }],
  };

  if (isUpdate) {
    return {
      ...basePayload,
      tenant_seller_info: { ...state.selectedGRN?.tenant_seller_info, gst_number: state.gstNumber, seller_type: state.selectedSeller?.seller_type },
      tenant_department_id: state.selectedGRN?.tenant_department_id,
      grn_tenant_id: state.selectedTenant || state.selectedGRN?.grn_tenant_id,
      grn_id: state.selectedGRN?.grn_id,
      seller_id: state.selectedGRN?.seller_id || null,
      grn_entity_type: state.selectedGRN?.grn_entity_type,
      update_document_reason: state.updateDocumentReason,
      grn_number: state.initialGrnNumber?.toLowerCase()?.trim() === state.grnNumber?.toLowerCase()?.trim() ? null : state.grnNumber,
    };
  }

  // Create-specific fields
  return {
    ...basePayload,
    grn_entity_id: state.selectedPoForGrn?.po_id,
    seller_id: state.selectedPoForGrn?.tenant_seller_info?.seller_id,
    tenant_department_id: state.tenantDepartmentId,
    tenant_seller_info: { ...state.selectedPoForGrn?.tenant_seller_info, internal_slr_code: state.selectedPoForGrn?.seller_info?.internal_slr_code, gst_number: state.gstNumber },
    grn_tenant_id: state.selectedTenant || state.selectedPoForGrn?.tenant_id,
    grn_entity_type: 'PURCHASE_ORDER',
    is_job_works_grn: state.selectedPoForGrn?.is_job_works_po,
    production_route_id: state.selectedPoForGrn?.production_route_id,
    product_sku_id: state.selectedPoForGrn?.product_sku_id,
    grn_number: state.initialGrnNumber?.toLowerCase()?.trim() === state.grnNumber?.toLowerCase()?.trim() ? null : state.grnNumber,
    seq_id: state.docSeqId || null,
  };
};

export const buildGrnLinesPayload = (data, options) => {
  const {
    isUpdate,
    grnEntityType,
    isMultipleBatchModeEnabled,
    tenantDepartmentId,
    grnTypeValue,
  } = options;

  // In create mode, filter out lines that haven't received any quantity.
  const relevantLines = isUpdate
    ? data
    : data?.filter((item) => Number(item.received_qty) > 0 || grnTypeValue === 'ADHOC');

  return relevantLines?.map((item) => {
    const isAdhocGRN = grnEntityType === 'GOOD_RECEIVING_NOTE';
    const lineQuantity = isUpdate && isAdhocGRN ? Number(item.quantity) : Number(item.received_qty);

    const getLineDiscount = () => {
      if (item?.lineDiscountType === 'Percent') {
        return Number(item?.discount || 0);
      }
      const total = (lineQuantity || Number(item.quantity)) * Number.parseFloat(item.offer_price);
      if (total === 0) return 0;
      return (Number(item?.discount || 0) / total) * 100;
    };

    const productBatches = isMultipleBatchModeEnabled
      ? (item.multiple_batch_info || []).map((batch) => ({
        ...batch,
        tenant_product_id: item.tenant_product_id || '',
        tenant_department_id: tenantDepartmentId,
        uom_id: item?.uomId || '',
        manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, item?.manufacturingDateFormat) : null,
        expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, item?.expiryDateFormat) : null,
        custom_fields: CustomFieldHelpers.postCfStructure(batch?.custom_fields),
      }))
      : (['NON_STORABLE', 'SERVICE'].includes(item?.product_sku_info?.product_type)
        ? []
        : [{
          ...item?.selectedBatch,
          quantity: grnTypeValue === 'ADHOC' ? Number(item.quantity) : lineQuantity,
          tenant_product_id: item.tenant_product_id || '',
          tenant_department_id: tenantDepartmentId,
          uom_id: item?.uomId || '',
          mrp: Number(item?.selectedBatch?.mrp) || 0,
          manufacturing_date: item?.selectedBatch?.manufacturing_date ? FormHelpers.dateFormatter(item?.selectedBatch?.manufacturing_date, item?.manufacturingDateFormat) : null,
          expiry_date: item?.selectedBatch?.expiry_date ? FormHelpers.dateFormatter(item?.selectedBatch?.expiry_date, item?.expiryDateFormat) : null,
          custom_fields: CustomFieldHelpers.postCfStructure(item?.selectedBatch?.custom_fields),
        }]);

    const grnLine = {
      grn_entity_line_id: item.grn_entity_line_id || item.po_line_id || null,
      is_adhoc_line: !item.grn_entity_line_id && !item.po_line_id,
      tenant_product_id: item.tenant_product_id || '',
      quantity: grnTypeValue === 'ADHOC' ? Number(item.quantity) : lineQuantity,
      invoice_quantity: item?.invoiceQuantity ?? (grnTypeValue === 'ADHOC' ? Number(item.quantity) : lineQuantity),
      secondary_uom_qty: item?.secondaryUomId ? item?.secondary_uom_qty : 0,
      offer_price: Number(item.offer_price) || 0,
      tax_id: item?.taxId || '',
      tax_group_info: item?.taxInfo,
      uom_id: item?.uomId || '',
      uom_info: Array.isArray(item?.uom_info) ? item?.uom_info : (item?.uom_info ? [item?.uom_info] : []),
      expiry_date: item.expiryDate,
      line_discount_percentage: getLineDiscount(),
      is_discount_in_percent: item?.lineDiscountType === 'Percent',
      remarks: item.remarks,
      tally_purchase_account: item.tally_purchase_account,
      production_route_line_id: item?.production_route_line_id,
      custom_fields: CustomFieldHelpers.postCfStructure(item?.lineCustomFields),
      product_batches: productBatches,
      // Fields specific to creation from PO
      mo_fg_id: item?.mo_fg_id,
      mo_line_id: item?.mo_line_id,
      product_sku_id: item?.product_sku_id,
      po_line_status: item?.po_line_status?.map((status) => ({ ...status, line_status_received_qty: Number(status?.line_status_received_qty) })),
    };

    return grnLine;
  });
};
