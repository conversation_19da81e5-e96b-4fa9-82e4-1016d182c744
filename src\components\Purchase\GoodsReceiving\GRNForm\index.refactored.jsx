import React, { useReducer, useEffect, Fragment, useMemo } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { usePara<PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Alert, DatePicker, Select, Upload, notification, Button, Drawer, Switch, Radio, Checkbox, Tooltip,
  Card,
} from 'antd';
import dayjs from 'dayjs';
import {
  PlusOutlined, PlusCircleFilled, EditFilled, UploadOutlined,
} from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark, faPlusCircle, faCircleInfo, } from '@fortawesome/free-solid-svg-icons';
import Constants, {
  INFINITE_EXPIRY_DATE, QUANTITY, DEFAULT_CUR_ROUND_OFF,
} from '@Apis/constants';
import closeIcon from '@Images/icons/icon-close-blue.png';

// Components
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import H3FormInput from '@Uilib/h3FormInput';
import H3Modal from '@Uilib/H3Modal';
import BatchesList from '@Components/Common/BatchesList';
import SelectSellerV2 from '../../../Common/SelectSellerV2';
import ViewPurchaseOrder from '../../../Common/ViewPurchaseOrder';
import SelectTaxType from '../../../Admin/Common/SelectTaxType';
import SelectDepartment from '@Components/Common/SelectDepartment';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import TagSelector from '@Components/Common/Selector/TagSelector';
import SelectExtraCharge from '../../../Admin/Common/SelectExtraCharge';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import FormLoadingSkull from '@Components/Common/formLoadingSkull';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import ErrorHandle from '../../../Common/ErrorHandle';
import VendorRating from '../../../Common/VendorRating';
import RestrictedAccessMessage from '../../../Common/RestrictedAccess/RestrictedAccessMessage';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import PRZButton from '../../../Common/UI/PRZButton';
import PRZConfirmationPopover from '../../../Common/UI/PRZConfirmationPopover';
import PRZInput from '../../../Common/UI/PRZInput';
import PRZText from '../../../Common/UI/PRZText';
import ChargesTaxInput from '../../../Common/ChargesTaxInput';
import PRZSelect from '../../../Common/UI/PRZSelect/index.jsx';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput/index.jsx';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import SelectPaymentRemark from '@Components/Common/SelectPaymentRemark';

// Helpers
import Helpers from '@Apis/helpers';
import SelectOrdersForGRN, { calculateCostPrice, computeUpdatedLineCFs, getClassNameHelper, handleDeleteHelper, restrictMessageHelper } from './helpers.jsx';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';

// Actions
import { GetPurchaseOrders } from '@Modules/purchase/purchaseOrder';
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import SellerActions from '@Actions/sellerActions';
import GRNActions from '@Actions/grnActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import OCRActions from '@Actions/ocrActions';
import TenantActions from '@Actions/tenantActions';
import TagActions from '@Actions/tagActions';
import ExtraChargesActions from '@Actions/configurations/extraChargesAction';
import VendorRatingActions from '../../../../actions/configurations/vendorRatingActions';
import InventoryLocationActions from '../../../../actions/settings/inventoryLocationActions';
import TaxActions from '@Actions/taxActions';

// Reducers, actions
import { formReducer, initialState, ACTIONS } from './reducer';

// Current File Imports
import GRNFormLines from './GRNFormLines';
import grnErrorList from './GrnErrors';
import './style.scss';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

const { Option } = Select;

const uploadButtonFormLevel = (
  <Button icon={<UploadOutlined />}>Click to Upload</Button>
);
const uploadButtonForUploadingAttachments = (
  <div>
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  </div>
);

function GRNForm({
  selectedPoForGrn,
  callback,
  workOrder,
}) {

  const {
    user,
    MONEY,
    priceMasking,
  } = useSelector((state) => state.UserReducers);

  const {
    createGRNLoading,
    getGRNByIdLoading,
    updateGRNLoading,
    selectedGRN,
  } = useSelector((state) => state.GRNReducers);

  const {
    sellers,
  } = useSelector((state) => state.SellerReducers);

  const {
    purchaseOrders,
    selectedPurchaseOrder,
  } = useSelector((state) => state.PurchaseOrderReducers);

  const {
    cfV2DocGoodReceivingNotes,
    getDocCFV2GoodReceivingNotesLoading,
  } = useSelector((state) => state.CFV2Reducers);

  const {
    inventoryLocations,
    getInventoryLocationsLoading,
  } = useSelector((state) => state.InventoryLocationReducers);

  const {
    getVendorRatingSuccess,
  } = useSelector((state) => state.VendorRatingReducers);

  const {
    CurrenciesResults,
    getCurrenciesLoading,
  } = useSelector((state) => state.CurrenciesReducers);

  const {
    createTagLoading,
  } = useSelector((state) => state.TagReducers);

  const {
    data: purchaseOrdersV2,
    loading: getPurchaseOrdersV2Loading,
  } = useSelector((state) => state.GetPurchaseOrders);

  const {
    taxesGroup,
    getTaxesLoading,
  } = useSelector((state) => state.TaxReducers);

  // dispatch
  const dispatch = useDispatch();

  // GRN Actions
  const addGRN = (payload, action) => dispatch(GRNActions.createGRN(payload, action));
  const updateGRNStatus = (payload, action) => dispatch(GRNActions.updateGRNStatus(payload, action));
  const updateGRN = (payload, action) => dispatch(GRNActions.updateGRN(payload, action));
  const getGRNById = (tenantId, grnId, callback) => dispatch(GRNActions.getGRNById(tenantId, grnId, callback));
  const getGRNByIdSuccess = (selectedGRN) => dispatch(GRNActions.getGRNByIdSuccess(selectedGRN));

  // Tenant
  const getTenantsConfiguration = (tenantId, callback) => dispatch(TenantActions.getTenantsConfiguration(tenantId, callback));

  // Seller
  const getSellers = (keyword, tenantId, page, limit, sellerId, callback) =>
    dispatch(SellerActions.getSellers(keyword, tenantId, page, limit, sellerId, callback));

  // Purchase Orders
  const getPurchaseOrderById = (poId, tenantId, callback) =>
    dispatch(PurchaseOrderActions.getPurchaseOrderById(poId, tenantId, callback));
  const getPurchaseOrderByIdSuccess = (selectedPo) =>
    dispatch(PurchaseOrderActions.getPurchaseOrderByIdSuccess(selectedPo));

  // CFV2 / Custom Fields
  const getDocCFV2 = (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback));
  const getDocCFV2Success = (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields));

  // Inventory / Vendor / Currency / Tags
  const getInventoryLocations = (tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable, tenantId, departmentId, callback) =>
    dispatch(InventoryLocationActions.getInventoryLocations(tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable, tenantId, departmentId, callback));

  const getVendorRating = (orgId, callback) => dispatch(VendorRatingActions.getVendorRating(orgId, callback));
  const getCurrencies = (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId));
  const createTag = (payload, callback) => dispatch(TagActions.createTag(payload, callback));

  // OCR
  const uploadDocumentOCR = (payload, action) => dispatch(OCRActions.uploadDocumentOCR(payload, action));

  // Extra Charges
  const getCharges = (orgId, entityName, callback) => dispatch(ExtraChargesActions.getCharges(orgId, entityName, callback));

  // Purchase Orders V2
  const getPurchaseOrdersV2 = (payload, callback) => dispatch(GetPurchaseOrders.actions.request(payload, callback));

  // Taxes
  const getTaxes = (orgId, page, limit, isActive, isGroup) =>
    dispatch(TaxActions.getTaxes(orgId, page, limit, isActive, isGroup));

  const [state, localDispatch] = useReducer(formReducer, initialState);

  const {
    // Dates
    grnDate,
    invoiceDate,
    dueDate,

    // GRN / PO
    grnTypeValue,
    data,
    chargeData,
    toggleRejectedBatches,
    showModal,
    getHistory,
    latestGrnId,
    selectedPoValue,
    discountPercentage,
    selectedPo,
    vendorAddress,
    formSubmitted,

    // Tax & Currency
    taxTypeName,
    selectedCurrencyInfo,
    selectedCurrencyID,
    charge1Name,
    charge1Value,
    discountType,
    freightTaxId,
    freightTax,
    openFreightTax,
    isAutomaticConversionRate,
    currencyConversionRate,
    freightTaxData,

    // Stock / Validation
    allStock,
    isLineWiseDiscount,
    isMultipleBatchModeEnabled,
    multipleBatchValidation,
    isBatchValid,
    visibleLineCfs,
    batchNumbers,

    // Tenant & Integration
    tenantDepartmentId,
    selectedTenant,
    selectedTenantTallyIntegrationId,

    // Misc
    updateDocumentReason,
    narration,
    paymentTerms,
    paymentRemarks,
    isAdhocGrnAllowed,
  } = state;

  const { grnId } = useParams();

  const dataForOcrGrn = useMemo(() => location?.state?.dataForGrn, [location]);

  // create grn from ocr data prefilling
  useEffect(() => {
    if (dataForOcrGrn && !selectedPoForGrn && user && data?.length === 0 && cfV2DocGoodReceivingNotes?.data?.success) {
      const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes.data.batch_custom_fields, true);

      const updatedData = dataForOcrGrn.grn_lines.map((item) => {
        const newBatch = {
          tenant_product_id: item?.tenant_product_id,
          expiry_date: item?.product_info?.expiry_days > 0
            ? dayjs().endOf('day').add(item.product_info.expiry_days, 'day')
            : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
          lot_number: '',
          custom_batch_number: `${item.product_info.internal_sku_code}/${dayjs().format('DDMMYY')}/${item.product_info.product_batch_counter}`,
          cost_price: item.offer_price || 0,
          mrp: item.selling_price || 0,
          manufacturingDateFormat: item.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: item.product_sku_info?.expiry_date_format,
          selling_price: 0,
          tenant_department_id: dataForOcrGrn.tenantDepartmentId,
          uom_id: item?.uom_id,
          manual_entry: false,
          inventory_location_id: item?.selectedLocation?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
          inventory_location_path: item?.selectedLocation?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
          is_rejected_batch: false,
          production_route_line_id: item?.production_route_line_id,
          custom_fields: batchCustomFields,
        };

        const parentKey = uuidv4();
        const discountValue = item.is_discount_in_percent
          ? Number.parseFloat(Number(item.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF))
          : Number.parseFloat(Number(item.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF)) || 0;
        const quantity = item.quantity ?? 0;
        const offerPrice = item.offer_price ?? 0;
        const taxableValue = item.is_discount_in_percent === 'Percent'
          ? (quantity * offerPrice) * (1 - discountValue / 100)
          : Math.max(quantity * offerPrice - discountValue, 0);

        const lineCFs = cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.length
          ? CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes.data.document_line_custom_fields.filter((i) => i?.is_active) || [], false)
          : [];

        return {
          ...item,
          product_sku_info: item?.product_info || item,
          manufacturingDateFormat: item.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: item.product_sku_info?.expiry_date_format,
          key: parentKey,
          product_sku_name: item.product_info?.product_sku_name,
          lineDiscountType: 'Percent',
          quantity: item.quantity || 0,
          secondaryUomUqc: item.product_sku_info?.secondary_uom_info?.uqc,
          secondaryUomId: item.product_sku_info?.secondary_uom_id,
          secondary_uom_qty: 0,
          offer_price: item.offer_price,
          taxId: item.tax_info?.tax_id,
          taxInfo: item.tax_info,
          uomId: item.uom_info?.uom_id || item.uom_id,
          productCategoryInfo: item.product_category_info || {},
          uomInfo: item.uom_info,
          uom_info: item.uom_info,
          received_qty: item.quantity || 0,
          remarks: item.product_info?.description?.replace(/<[^>]+>/g, ''),
          remarkRequired: !!item.product_info?.description?.replace(/<[^>]+>/g, ''),
          product_batches: [],
          child_taxes: Helpers.computeTaxation(taxableValue, item.tax_info, item.selectedPo?.billing_address?.state, item.selectedPo?.seller_address?.state)?.tax_info?.child_taxes,
          selectedBatch: newBatch,
          lineCustomFields: lineCFs,
          available_batches: item.product_batches
            ? [
              { ...newBatch },
              ...item.product_batches.map((batch) => ({
                ...batch,
                custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
              })),
            ]
            : [{ ...newBatch }],
          multiple_batch_info: [{
            key: uuidv4(),
            ...newBatch,
            parentKey,
            sq_no: 1,
            quantity: '',
            ar_number: '',
            expiryDays: item.product_sku_info?.expiry_days,
          }],
        };
      });

      localDispatch({
        type: 'INIT_STATE',
        payload: {
          data: updatedData,
          selectedPo: dataForOcrGrn?.selectedPo,
          tenantDepartmentId: dataForOcrGrn?.tenantDepartmentId || user?.tenant_info?.default_store_id,
          selectedTenantSeller: dataForOcrGrn?.selectedSeller?.tenant_seller_id,
          gstNumber: dataForOcrGrn?.selectedPo?.tenant_seller_info?.gst_number,
          selectedSeller: dataForOcrGrn?.selectedSeller,
          shippingAddress: dataForOcrGrn?.selectedPo?.shipping_address,
          vendorAddress: dataForOcrGrn?.grnTypeValue === 'ADHOC' ? dataForOcrGrn?.vendorAddress : dataForOcrGrn?.selectedPo?.seller_address,
          selectedAddressType: 'SELLER',
          invoiceDate: dataForOcrGrn?.invoice_date ? dayjs(dataForOcrGrn.invoice_date, 'DD-MM-YYYY') : null,
          invoiceNumber: dataForOcrGrn?.invoice_number,
          grnTypeValue: dataForOcrGrn?.grnTypeValue,
          selectedPoValue: dataForOcrGrn?.selectedPoValue,
          fileList: dataForOcrGrn?.ocrDocData,
          selectedCurrencyInfo: dataForOcrGrn?.selectedPo ? dataForOcrGrn?.selectedPo?.org_currency_info : '',
          selectedCurrencyID: dataForOcrGrn?.selectedPo ? dataForOcrGrn?.selectedPo?.org_currency_info?.org_currency_id : '',
          isAutomaticConversionRate: dataForOcrGrn?.selectedPo ? false : null,
          currencyConversionRate: dataForOcrGrn?.selectedPo ? dataForOcrGrn.selectedPo?.conversion_rate : '',
        },
      });
    }
  }, [dataForOcrGrn, selectedPoForGrn, user, cfV2DocGoodReceivingNotes]);

  useEffect(() => {
    // Reset selected GRN
    getGRNByIdSuccess(null);

    // Vendor rating
    getVendorRating(user?.tenant_info?.org_id);

    // Fetch GRN by Id if grnId exists
    if (grnId) {
      const tenantIds = Helpers.getTenantEntityPermission(
        user?.user_tenants,
        Helpers.permissionEntities.GOOD_RECEIVING,
        Helpers.permissionTypes.READ
      ).join(',');

      getGRNById(tenantIds, grnId, (grn) => {
        const linkedPoIds = [...new Set(grn?.grn_lines?.map((item) => item?.po_id))].filter(Boolean).join(',');

        if (grn?.tenant_info?.tenant_id && grn?.tenant_seller_info?.tenant_seller_id) {
          getPurchaseOrdersV2({
            query: {
              org_id: user?.org_id,
              tenant_id: grn?.tenant_info?.tenant_id,
              tenant_seller_id: grn?.tenant_seller_info?.tenant_seller_id,
              status: 'ISSUED',
              page: 1,
              limit: 30,
              exclude_job_works_po: true,
              exclude_subcontractor_po: true,
              linked_po_ids: linkedPoIds,
            },
          });
        }

        getInventoryLocations(
          grn?.tenant_department_id,
          '',
          null,
          true,
          null,
          null,
          (location) => {
            localDispatch({
              type: ACTIONS.INIT_STATE,
              payload: {
                inventoryLocationId: location?.inventory_location?.find((i) => i?.is_active)?.inventory_location_id,
                inventoryLocationPath: location?.inventory_location?.find((i) => i?.is_active)?.inventory_location_path,
                grnNumber: grn?.grn_number || '',
              },
            });
          }
        );
      });
    }

    // Fetch CFV2 custom fields
    getDocCFV2({ orgId: user?.tenant_info?.org_id, entityName: 'GOOD_RECEIVING_NOTES,BATCH' });

    // Selected PO handling
    if (
      selectedPoForGrn &&
      (!inventoryLocations ||
        inventoryLocations?.inventory_location?.find((i) => i?.is_active)?.tenant_department_id !== selectedPoForGrn?.tenant_department_id)
    ) {
      getInventoryLocations(
        selectedPoForGrn?.tenant_department_id,
        '',
        null,
        true,
        null,
        null,
        (location) => {
          localDispatch({
            type: ACTIONS.INIT_STATE,
            payload: {
              inventoryLocationId: location?.inventory_location?.find((i) => i?.is_active)?.inventory_location_id,
              inventoryLocationPath: location?.inventory_location?.find((i) => i?.is_active)?.inventory_location_path,
              tenantDepartmentId: selectedPoForGrn?.tenant_department_id,
              selectedTenant: selectedPoForGrn?.tenant_id,
              selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedPoForGrn?.tenant_id)?.it_id,
            },
          });
        }
      );
    }

    // Currencies
    getCurrencies();

    // Charges (Freight SAC)
    getCharges(user?.tenant_info?.org_id, 'PURCHASE', (charges) => {
      const freight = charges?.find((i) => i?.ledger_name === 'Freight Charge' && i?.is_system_charge);
      if (freight) {
        localDispatch({ type: ACTIONS.INIT_STATE, field: 'freightSacCode', value: freight?.charge_sac_code });
      }
    });

    // Taxes
    getTaxes(user?.tenant_info?.org_id, 1, 1000, null, true);

  }, [user, grnId, selectedPoForGrn, inventoryLocations]);

  // adhoc grn
  useEffect(() => {
    if (grnId && !selectedPoForGrn?.po_id) {
      if (user && selectedGRN && !data?.length && cfV2DocGoodReceivingNotes?.data?.success) {
        getSellers('', selectedTenant, 1, 30, selectedGRN?.tenant_seller_id);
        if (selectedGRN) {
          getPurchaseOrderById(
            selectedGRN?.grn_entity_id,
            Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','),
            () => { }
          );
        }

        const getAvailableBatches = (item) => {
          if (!item) return [];

          const productBatches = Array.isArray(item.product_batches) ? item.product_batches : [];
          const availableBatches = Array.isArray(item.available_batches) ? item.available_batches : [];

          if (productBatches.length > 0 && productBatches[0]?.batch_id) {
            return availableBatches;
          }

          if (availableBatches.length > 0) {
            return [...productBatches, ...availableBatches];
          }

          return productBatches;
        };

        const checkGRNType = () => {
          if (selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE') return 'ADHOC';
          if (selectedGRN?.grn_entity_type === 'PURCHASE_ORDER' && !selectedGRN?.grn_entity_id) return 'MULTIPO';
          return 'Purchase Order';
        };

        const updatedGrnData = selectedGRN?.grn_lines?.map((item) => {
          const discountValue = item?.is_discount_in_percent
            ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF))
            : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));

          const taxableValue = item?.is_discount_in_percent
            ? item.received_qty * item.offer_price * (1 - discountValue / 100)
            : Math.max(item.received_qty * item.offer_price - discountValue, 0);

          let productBatchPoToGrn = getAvailableBatches(item)?.filter((batch) => !batch.is_rejected_batch)?.[0];
          productBatchPoToGrn = {
            ...productBatchPoToGrn,
            custom_fields: CustomFieldHelpers.getCfStructure(productBatchPoToGrn?.custom_fields, true),
          };

          let productBatchesAdhocGrn = item?.product_batches?.[0];
          productBatchesAdhocGrn = {
            ...productBatchesAdhocGrn,
            custom_fields: CustomFieldHelpers.getCfStructure(productBatchesAdhocGrn?.custom_fields, true),
          };

          return {
            ...item,
            key: uuidv4(),
            taxId: item?.tax_group_info?.tax_id,
            taxInfo: item?.tax_group_info,
            child_taxes: Helpers.computeTaxation(
              taxableValue,
              item?.tax_group_info,
              selectedGRN?.tenant_billing_address?.state,
              selectedGRN?.seller_address_info?.state
            )?.tax_info?.child_taxes,
            uomId: item?.uom_info[0]?.uom_id,
            uomInfo: item?.uom_info[0],
            uom_info: item?.uom_info[0],
            uomGroup: item?.product_sku_info?.group_id || item?.product_sku_info?.purchase_uom_info?.group_id,
            uom_list: item?.product_sku_info?.uom_list,
            expiryDate: item.expiry_date,
            otherGrnQty: item?.ordered_qty - item?.total_received_qty,
            available_batches: getAvailableBatches(item),
            secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
            secondaryUomId: item?.product_sku_info?.secondary_uom_id,
            secondary_uom_qty: item?.secondary_uom_qty,
            manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
            expiryDateFormat: item?.product_sku_info?.expiry_date_format,
            selectedBatch: selectedGRN?.grn_entity_type === 'GOOD_RECEIVING_NOTE' ? productBatchesAdhocGrn : productBatchPoToGrn,
            expiryDays: item?.product_sku_info?.expiry_days,
            remarks: item?.remark,
            remarkRequired: !!item?.remark?.replace(/<[^>]+>/g, ''),
            discount: discountValue,
            quantity: item?.received_qty,
            invoice_quantity: item?.invoice_quantity,
            po_line_id: item?.grn_entity_line_id,
            tally_purchase_account: item?.tally_purchase_account,
            nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
            product_sku_name: item?.product_sku_info?.product_sku_name,
            lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
            productCategoryInfo: item?.product_sku_info?.product_category_info || {},
            multiple_batch_info: item?.product_batches?.map((batch) => ({
              key: uuidv4(),
              ...batch,
              grn_line_id: item?.grn_line_id,
              custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true),
            })),
            lineCustomFields: CustomFieldHelpers.mergeCustomFields(
              cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields.filter((cf) => cf?.is_active),
              item?.grn_line_custom_fields || []
            ),
          };
        });

        localDispatch({
          type: ACTIONS.INIT_STATE,
          payload: {
            selectedPoValue: selectedGRN?.linked_pos?.map((item) => ({
              key: item?.po_id,
              value: item?.po_id,
              label: [`#${item?.po_number}`, ''],
            })),
            data: updatedGrnData,
            selectedPo: selectedGRN?.grn_entity_id,
            checkedRecipients: selectedGRN?.is_automatic_notification_enabled,
            toRecipients: selectedGRN?.notification_recipients || [],
            tenantDepartmentId: selectedGRN?.tenant_department_id,
            grnTypeValue: checkGRNType(),

            terms: selectedGRN?.remark,
            selectedTenantSeller: selectedGRN?.tenant_seller_info?.tenant_seller_id,
            gstNumber: selectedGRN?.tenant_seller_info?.gst_number,
            selectedSellerId: selectedGRN?.seller_id,
            grnDate: dayjs(selectedGRN?.grn_date_time),
            ewayBillNumber: selectedGRN?.e_way_bill_number,
            ewayBillList: selectedGRN?.e_way_bill,
            invoiceDate: selectedGRN?.invoice_date ? dayjs(selectedGRN?.invoice_date) : null,
            invoiceNumber: selectedGRN?.invoice_number,
            fileList: selectedGRN?.attachments,
            purchaseAccount: selectedGRN?.tally_purchase_account,
            dueDate: selectedGRN?.grn_due_date ? dayjs(selectedGRN?.grn_due_date) : null,
            chargeData:
              selectedGRN?.other_charges?.map((otherCharge) => ({
                ...otherCharge,
                chargeKey: uuidv4(),
                chargesTaxId: otherCharge?.tax_info?.tax_id,
                chargesSacCode: otherCharge?.charge_sac_code,
                chargesTaxInfo: otherCharge?.tax_info,
                chargesTax: otherCharge?.tax_info?.tax_value,
                tallyLedgerName: otherCharge?.ledger_name || null,
                chargesTaxData: {
                  ...otherCharge?.tax_info,
                  child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, otherCharge?.tax_info, selectedGRN?.tenant_billing_address?.state, selectedGRN?.seller_address_info?.state)?.tax_info?.child_taxes,
                },
              })) || [],
            discountPercentage: selectedGRN?.is_discount_in_percent ? selectedGRN?.discount_percentage : selectedGRN?.discount_amount,
            discountType: selectedGRN?.is_discount_in_percent ? 'Percent' : 'Amount',
            isLineWiseDiscount: !selectedGRN?.is_line_wise_discount,
            taxType: selectedGRN?.tcs_id ?? selectedGRN?.tds_id,
            taxTypeId: selectedGRN?.tcs_info ? selectedGRN?.tcs_info?.tax_id : selectedGRN?.tds_info?.tax_id,
            taxTypeInfo: selectedGRN?.tcs_info ?? selectedGRN?.tds_info,
            selectedCurrencyInfo: selectedGRN?.org_currency_info,
            selectedCurrencyID: selectedGRN?.org_currency_info?.org_currency_id,
            taxTypeName: selectedGRN?.tcs_info ? selectedGRN?.tcs_info?.tax_type_name : selectedGRN?.tds_info?.tax_type_name,
            charge1Name: selectedGRN?.charge_1_name || 'Freight',
            charge1Value: selectedGRN?.charge_1_value,
            freightTaxId: selectedGRN?.freight_tax_id || 'Not Applicable',
            freightTax: selectedGRN?.freight_tax_info?.tax_value,
            freightTaxInfo: selectedGRN?.freight_tax_info,
            freightTaxData: {
              ...selectedGRN?.freight_tax_info,
              child_taxes: Helpers.computeTaxation(selectedGRN?.charge_1_value, selectedGRN?.freight_tax_info, selectedGRN?.tenant_billing_address?.state, selectedGRN?.seller_address_info?.state)?.tax_info?.child_taxes
            },
            freightSacCode: selectedGRN?.freight_sac_code,
            createExpenseCheckbox: selectedGRN?.transporter_id || selectedGRN?.vehicle_number,
            transporterId: selectedGRN?.transporter_id,
            vehicleNumber: selectedGRN?.vehicle_number,
            transporterBillNumber: selectedGRN?.transporter_bill_number,
            billingAddress: user?.tenant_info?.state,
            vendorAddress: selectedGRN?.seller_address_info,
            selectedTags: selectedGRN?.tags,
            isAutomaticConversionRate: false,
            currencyConversionRate: selectedGRN?.conversion_rate,
            selectedTenant: selectedGRN?.tenant_info?.tenant_id,
            selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedGRN?.tenant_info?.tenant_id)?.it_id,
            narration: selectedGRN?.narration || '',
            isBatchValid: true,
            paymentTerms: selectedGRN?.payment_terms?.[0]?.due_days || 0,
            paymentRemarks: selectedGRN?.payment_terms?.[0]?.remark || '',
          },
        });
      }

      if (selectedGRN?.grn_id && cfV2DocGoodReceivingNotes?.data?.success) {
        const oldCustomField = selectedGRN?.custom_fields || [];
        const oldLineCustomField = selectedGRN.grn_lines?.[0]?.grn_line_custom_fields || [];

        localDispatch({
          type: ACTIONS.INIT_STATE,
          payload: {
            cfGoodReceivingNotesDoc: CustomFieldHelpers.mergeCustomFields(cfV2DocGoodReceivingNotes?.data?.document_custom_fields, oldCustomField),
            cfGoodReceivingNotesLine: CustomFieldHelpers.mergeCustomFields(cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields, oldLineCustomField) || [],
            visibleLineCfs: CustomFieldHelpers.mergeCustomFields(cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields, oldLineCustomField)
              .filter((cf) => cf.isActive) || [],
          },
        });
      }
    }
  }, [
    grnId,
    user,
    selectedGRN,
    cfV2DocGoodReceivingNotes,
    selectedPoForGrn,
  ]);

  // create grn from po
  useEffect(() => {
    if (
      selectedPoForGrn?.po_id &&
      !data?.length &&
      selectedPoForGrn &&
      inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.tenant_department_id === selectedPoForGrn?.tenant_department_id &&
      cfV2DocGoodReceivingNotes?.data?.success &&
      !selectedPoForGrn &&
      taxesGroup?.success
    ) {
      const isApInvoiceEnabled =
        user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.settings?.is_enabled &&
        user?.tenant_info?.purchase_config?.sub_modules?.account_payable_invoice?.is_active;

      const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.batch_custom_fields, true);

      const isVendorOverseas =
        (selectedPoForGrn?.seller_info?.seller_type === 'OVERSEAS' || selectedPoForGrn?.seller_type === 'OVERSEAS') &&
        user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

      const grnData = selectedPoForGrn?.purchase_order_lines?.map((item) => {

        const newBatch = {
          tenant_product_id: item?.tenant_product_id,
          expiry_date: item?.product_sku_info?.expiry_days > 0
            ? dayjs().endOf('day').add(item?.product_sku_info?.expiry_days, 'day')
            : dayjs(INFINITE_EXPIRY_DATE, 'YYYY-MM-DD').endOf('day'),
          lot_number: '',
          custom_batch_number: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
          cost_price: calculateCostPrice(item, selectedPoForGrn, isApInvoiceEnabled),
          mrp: item?.product_sku_info?.selling_price || 0,
          manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: item?.product_sku_info?.expiry_date_format,
          selling_price: 0,
          tenant_department_id: selectedPoForGrn?.tenant_department_id,
          uom_id: item?.uom_id,
          manual_entry: false,
          inventory_location_id: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_id,
          inventory_location_path: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.inventory_location_path,
          is_rejected_batch: false,
          production_route_line_id: item?.production_route_line_id,
          custom_fields: batchCustomFields,
        };

        const discountValue = item?.is_discount_in_percent
          ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF))
          : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));

        const parentKey = uuidv4();

        return {
          ...item,
          key: parentKey,
          received_qty: '',
          total_pending_qty: 0,
          taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : item?.tax_id,
          taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_group_info,
          child_taxes: Helpers.computeTaxation(
            item.received_qty * item.offer_price,
            isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : item?.tax_group_info,
            selectedPoForGrn?.billing_address?.state,
            selectedPoForGrn?.seller_address?.state
          )?.tax_info?.child_taxes,
          uomId: item?.uom_id,
          uomInfo: item?.uom_info,
          discount: discountValue,
          discountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
          selectedBatch: newBatch,
          secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
          secondaryUomId: item?.product_sku_info?.secondary_uom_id,
          secondary_uom_qty: 0,
          manufacturingDateFormat: item?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: item?.product_sku_info?.expiry_date_format,
          available_batches: item?.available_batches
            ? [
              { ...newBatch },
              ...(item?.available_batches || []).map((batch) => ({ ...batch, custom_fields: CustomFieldHelpers.getCfStructure(batch?.custom_fields, true) })),
            ]
            : [{ ...newBatch }],
          expiryDays: item?.product_sku_info?.expiry_days,
          productCategoryInfo: item?.product_sku_info?.product_category_info || {},
          tally_purchase_account: item?.product_sku_info?.tally_purchase_account,
          nextBatchCode: `${item?.product_sku_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.product_sku_info?.product_batch_counter}`,
          remarks: item?.remark?.replace(/<[^>]+>/g, ''),
          remarkRequired: !!item?.remark?.replace(/<[^>]+>/g, ''),
          lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
          multiple_batch_info: [
            {
              key: uuidv4(),
              ...newBatch,
              parentKey,
              sq_no: 1,
              quantity: '',
              ar_number: '',
              expiryDays: item?.product_sku_info?.expiry_days,
            },
          ],
          lineCustomFields: FormHelpers.lineSystemFieldValue(
            CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.filter((cf) => cf?.is_active)),
            item?.po_line_custom_fields
          ),
          lineStatusPendingQty: 0,
          po_line_status: item?.po_line_status?.map((line) => ({ ...line, line_status_received_qty: 0 })),
          offer_price: isApInvoiceEnabled ? 0 : item?.offer_price,
        };
      });

      const grnChargeData = selectedPoForGrn?.other_charges?.map((otherCharge) => ({
        ...otherCharge,
        chargeKey: uuidv4(),
        chargesTaxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : otherCharge?.tax_info?.tax_id,
        chargesSacCode: otherCharge?.charge_sac_code,
        chargesTaxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info,
        chargesTax: isVendorOverseas ? 0 : otherCharge?.tax_info?.tax_value,
        tallyLedgerName: otherCharge?.ledger_name || null,
        chargesTaxData: {
          ...otherCharge?.tax_info,
          child_taxes: Helpers.computeTaxation(
            otherCharge?.charge_amount,
            isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : otherCharge?.tax_info,
            selectedPoForGrn?.billing_address?.state,
            selectedPoForGrn?.seller_address?.state
          )?.tax_info?.child_taxes,
        },
      })) || [];

      const dueDate = dayjs().add(Number(selectedPoForGrn?.payment_terms?.[0]?.due_days || 0), 'days');

      const oldLineCustomField = selectedPoForGrn?.purchase_order_lines?.[0]?.po_line_custom_fields || [];
      const oldCustomField = selectedPoForGrn?.custom_fields || [];

      localDispatch({
        type: ACTIONS.INIT_STATE,
        payload: {
          selectedTenant: selectedPoForGrn?.tenant_id,
          fileList: selectedPoForGrn?.attachments,
          data: FormHelpers.updatedBatchNumber(grnData),
          chargeData: grnChargeData,
          tenantDepartmentId: selectedPoForGrn?.tenant_department_id,
          dueDate,
          discountPercentage: selectedPoForGrn.is_discount_in_percentage
            ? selectedPoForGrn?.discount_percentage.toFixed(DEFAULT_CUR_ROUND_OFF)
            : selectedPoForGrn?.discount_amount.toFixed(DEFAULT_CUR_ROUND_OFF),
          discountType: selectedPoForGrn.is_discount_in_percent ? 'Percent' : 'Amount',
          isLineWiseDiscount: !selectedPoForGrn?.is_line_wise_discount,
          taxType: isVendorOverseas ? null : selectedPoForGrn?.tcs_id ?? selectedPoForGrn?.tds_id,
          taxTypeId: isVendorOverseas
            ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id
            : selectedPoForGrn?.tcs_info?.tax_id ?? selectedPoForGrn?.tds_info?.tax_id,
          taxTypeInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : selectedPoForGrn?.tcs_info ?? selectedPoForGrn?.tds_info,
          selectedCurrencyID: selectedPoForGrn?.org_currency_info?.org_currency_id,
          selectedCurrencyInfo: selectedPoForGrn?.org_currency_info,
          taxTypeName: isVendorOverseas ? '' : selectedPoForGrn?.tcs_info?.tax_type_name ?? selectedPoForGrn?.tds_info?.tax_type_name,
          charge1Name: selectedPoForGrn?.charge_1_name || 'Freight',
          gstNumber: selectedPoForGrn?.tenant_seller_info?.gst_number,
          charge1Value: selectedPoForGrn?.charge_1_value,
          selectedTags: selectedPoForGrn?.tags,
          freightTaxId: selectedPoForGrn?.freight_tax_id || 'Not Applicable',
          freightTax: selectedPoForGrn?.freight_tax_info?.tax_value,
          freightTaxInfo: selectedPoForGrn?.freight_tax_info,
          freightTaxData: {
            ...selectedPoForGrn?.freight_tax_info,
            child_taxes: Helpers.computeTaxation(
              selectedPoForGrn?.charge_1_value,
              isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : selectedPoForGrn?.freight_tax_info,
              selectedPoForGrn?.billing_address?.state,
              selectedPoForGrn?.seller_address?.state
            )?.tax_info?.child_taxes,
          },
          freightSacCode: selectedPoForGrn?.freight_sac_code,
          createExpenseCheckbox: selectedPoForGrn?.transporter_id || selectedPoForGrn?.vehicle_number,
          transporterId: selectedPoForGrn?.transporter_id,
          vehicleNumber: selectedPoForGrn?.vehicle_number,
          transporterBillNumber: selectedPoForGrn?.transporter_bill_number,
          cfGoodReceivingNotesLine: CustomFieldHelpers.mergeCustomFields(cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields, oldLineCustomField) ?? [],
          cfGoodReceivingNotesDoc: CustomFieldHelpers.mergeCustomFields(cfV2DocGoodReceivingNotes?.data?.document_custom_fields, oldCustomField) ?? [],
          visibleLineCfs: CustomFieldHelpers.mergeCustomFields(cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields, oldLineCustomField)
            .filter((item) => item.isActive) ?? [],
          isAutomaticConversionRate: false,
          currencyConversionRate: selectedPoForGrn?.conversion_rate,
          vendorAddress: selectedPoForGrn?.seller_address,
          selectedSeller: selectedPoForGrn?.seller_info,
          paymentTerms: selectedPoForGrn?.payment_terms?.[0]?.due_days || 0,
          paymentRemarks: selectedPoForGrn?.payment_terms?.[0]?.remark || '',
        },
      });
    }
  }, [
    selectedPoForGrn,
    inventoryLocations,
    cfV2DocGoodReceivingNotes,
    taxesGroup,
    user,
  ]);

  // initiliazing customfields and tenant for GRNForm
  useEffect(() => {
    if (cfV2DocGoodReceivingNotes?.data?.success && !selectedPoForGrn?.po_id) {
      const lineCFs = cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.document_line_custom_fields?.filter((item) => item?.is_active) || [], false) : [];

      localDispatch({
        type: ACTIONS.INIT_STATE,
        payload: {
          cfGoodReceivingNotesDoc: CustomFieldHelpers.getCfStructure(cfV2DocGoodReceivingNotes?.data?.document_custom_fields, true) || [],
          cfGoodReceivingNotesLine: lineCFs,
          visibleLineCfs: lineCFs,
          selectedTenant: user?.tenant_info?.tenant_id,
        },
      });
    }
  }, [cfV2DocGoodReceivingNotes, user]);

  // setting default currency for GRNForm
  useEffect(() => {
    if (
      CurrenciesResults &&
      CurrenciesResults.length > 0 &&
      (grnTypeValue === 'ADHOC' || dataForOcrGrn || selectedPoForGrn || selectedGRN)
    ) {
      const defaultCurrency = CurrenciesResults.find((currency) => currency.is_default === true);

      const currencyInfo = () => {
        if (selectedPoForGrn) {
          return selectedPoForGrn?.org_currency_info;
        } else if (selectedGRN) {
          return selectedGRN?.org_currency_info;
        } else if (dataForOcrGrn) {
          return dataForOcrGrn?.selectedPo?.org_currency_info;
        } else {
          return defaultCurrency;
        }
      };

      const getConversionRate = () => {
        if (selectedPoForGrn) {
          return selectedPoForGrn?.conversion_rate;
        } else if (selectedGRN) {
          return selectedGRN?.conversion_rate;
        } else if (dataForOcrGrn) {
          return dataForOcrGrn?.selectedPo?.conversion_rate;
        } else {
          return user?.tenant_info?.global_config?.settings?.automatic_conversion_rate
            ? defaultCurrency?.automatic_conversion_rate
            : defaultCurrency?.conversion_rate;
        }
      };

      const checkAutomaticConversionRate = () => {
        return (selectedPoForGrn || selectedGRN || dataForOcrGrn) ? false : user?.tenant_info?.global_config?.settings?.automatic_conversion_rate;
      };

      localDispatch({
        type: ACTIONS.INIT_STATE,
        payload: {
          selectedCurrencyID: currencyInfo()?.org_currency_id,
          selectedCurrencyInfo: currencyInfo(),
          currencyConversionRate: getConversionRate(),
          isAutomaticConversionRate: checkAutomaticConversionRate(),
        },
      });

    }
  }, [CurrenciesResults, grnTypeValue, dataForOcrGrn, selectedPoForGrn, selectedGRN, user]);

  const getBillFromAddress = useMemo(() => {
    let billFromAddress;
    if (selectedPo) {
      billFromAddress = selectedPo?.billing_address?.state;
    } else if (selectedPoForGrn) {
      billFromAddress = selectedPoForGrn?.billing_address?.state;
    } else if (selectedGRN) {
      billFromAddress = selectedGRN?.tenant_billing_address?.state;
    } else {
      billFromAddress = user?.tenant_info?.state;
    }
    return billFromAddress;
  }, [selectedPo, selectedPoForGrn, selectedGRN, user]);

  const getBillToAddress = useMemo(() => {
    let billToAddress;
    // eslint-disable-next-line unicorn/prefer-ternary
    if (selectedPoForGrn) {
      billToAddress = selectedPoForGrn?.seller_address?.state;
    } else {
      billToAddress = vendorAddress?.state;
    }
    return billToAddress ?? selectedPoForGrn?.seller_address?.state;
  }, [selectedPoForGrn, vendorAddress]);

  const handleDelete = (key, id) => {
    localDispatch({
      type: ACTIONS.DELETE_ITEM,
      payload: { key, id, billFromAddress: getBillFromAddress(), billToAddress: getBillToAddress() },
    });
  };

  const handleDeleteCharge = (chargeKey) => {
    localDispatch({
      type: ACTIONS.DELETE_CHARGE,
      payload: { chargeKey },
    });
  };

  const handleFullQuantity = (item) => {
    localDispatch({
      type: ACTIONS.HANDLE_FULL_QUANTITY,
      payload: {
        item,
        billFromAddress: getBillFromAddress(),
        billToAddress: getBillToAddress(),
      },
    });
  };

  const recordFullQuantity = () => {
    localDispatch({
      type: ACTIONS.RECORD_FULL_QUANTITY,
      payload: {
        billFromAddress: getBillFromAddress(),
        billToAddress: getBillToAddress(),
      },
    });
  };

  const removeZeroQuantity = () => {
    localDispatch({
      type: ACTIONS.REMOVE_ZERO_QUANTITY,
    });
  };

  const updateTableValue = (key, value, label) => {
    localDispatch({
      type: ACTIONS.UPDATE_TABLE_VALUE,
      payload: { key, value, label },
    });
  };

  const getClassName = (isFlexible, isOverflow, item) => {
    return getClassNameHelper(isFlexible, isOverflow, item, formSubmitted);
  };

  const customInputChange = (fieldValue, cfId) => {
    localDispatch({
      type: ACTIONS.CUSTOM_INPUT_CHANGE,
      payload: { fieldValue, cfId },
    });
  };

  const customLineInputChange = (
    fieldValue,
    cfId,
    record,
    returnCfData = false,
  ) => {
    if (returnCfData) {
      // ✅ compute-only path – returns updated line CFs for the caller
      return computeUpdatedLineCFs(
        record?.lineCustomFields ?? [],
        cfId,
        fieldValue,
      );
    }

    // ✅ state update path – reducer will update data and (optionally) re-apply discount
    localDispatch({
      type: ACTIONS.CUSTOM_LINE_INPUT_CHANGE,
      payload: {
        fieldValue,
        cfId,
        record,
        discountPercentage,
        isLineWiseDiscount,
        grnTypeValue,
        billFromAddress: getBillFromAddress(),
        billToAddress: getBillToAddress(),
      },
    });
  };

  // component wrapper — call this instead of doing logic here
  const handleProductChange = (tenantSku, key, productData, isMultiMode, callback) => {
    localDispatch({
      type: ACTIONS.HANDLE_PRODUCT_CHANGE,
      payload: {
        tenantSku,
        key,
        productData,
        isMultiMode,
        callback,
        user,
        inventoryLocations,
        cfV2DocGoodReceivingNotes,
        taxesGroup,
        selectedPoForGrn,
      },
    });
  };

  const handleMultiProductChange = (tenantSku, key, productData, callback) => {
    handleProductChange(tenantSku, key, productData, true, callback);
  };

  const handleProductChangeValue = (value, key) => {
    localDispatch({
      type: ACTIONS.HANDLE_PRODUCT_CHANGE_VALUE,
      payload: { value, key },
    });
  };

  const addNewRow = () => {
    localDispatch({
      type: ACTIONS.ADD_NEW_ROW,
    });
  };

  const toggleBatchInner = (record, adjustmentRow) => {
    localDispatch({
      type: ACTIONS.TOGGLE_BATCH_INNER,
      payload: { record, adjustmentRow },
    });
  };

  const toggleBatch = (record, adjustmentRow) => {
    localDispatch({
      type: ACTIONS.TOGGLE_BATCH,
      payload: { record, adjustmentRow },
    });
  };

  const addNewChargesRow = () => {
    localDispatch({
      type: ACTIONS.ADD_NEW_CHARGE_ROW,
    });
  };

  const addNewBatch = (batch, callback) => {
    localDispatch({
      type: ACTIONS.ADD_NEW_BATCH,
      payload: { batch, callback },
    });
  };

  const removeNewBatch = () => {
    localDispatch({
      type: ACTIONS.REMOVE_BATCH,
    });
  };

  const setSelectedBatch = (batch) => {
    localDispatch({
      type: ACTIONS.SET_SELECTED_BATCH,
      payload: { batch },
    });
  };

  const updateLandedCost = (data, chargeData, charge1Value) => {
    localDispatch({
      type: ACTIONS.UPDATE_LANDED_COST,
      payload: {
        data,
        chargeData,
        charge1Value,
        isLineWiseDiscount,
        discountPercentage,
        discountType,
        grnTypeValue,
        billFromAddress: getBillFromAddress(),
        billToAddress: getBillToAddress(),
      },
    });
  };

  const customFieldVisibilityChange = (visible, cfId) => {
    localDispatch({
      type: ACTIONS.CUSTOM_FIELD_VISIBILITY_CHANGE,
      payload: { visible, cfId },
    });
  };

  const onBulkUploadBatch = (batchData, lineId) => {
    localDispatch({
      type: ACTIONS.BULK_UPLOAD_BATCH,
      payload: { batchData, lineId },
    });
  };

  const restrictMessage = () => {
    return restrictMessageHelper({ priceMasking, grnTypeValue, isAdhocGrnAllowed });
  };

  return (
    <Fragment>
      <div>
        {getGRNByIdLoading ? (
          <FormLoadingSkull />
        ) : (
          <Fragment>
            <div
              className="form__wrapper"
              style={{ paddingTop: (selectedPoForGrn?.po_id) ? '0px' : '90px' }}
            >
              New GRN Form
            </div>
          </Fragment>
        )}
      </div>
    </Fragment>
  );
}

export default GRNForm;